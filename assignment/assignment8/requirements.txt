# Semantic Search Engine Assignment Requirements
# Install with: pip install -r requirements.txt

# Azure OpenAI and OpenAI libraries
openai>=1.0.0

# Scientific computing and data analysis
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Jupyter notebook support
jupyter>=1.0.0
ipython>=7.0.0
ipywidgets>=7.6.0

# Additional utilities
tqdm>=4.62.0
requests>=2.25.0
python-dotenv>=0.19.0

# Optional: For advanced text processing
# nltk>=3.6.0
# spacy>=3.4.0
