{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Semantic Search Engine for Clothing Products\n", "\n", "## Assignment 8: Building a Semantic Search Engine with Text Embeddings\n", "\n", "### Objectives\n", "- Learn to generate text embeddings using Azure OpenAI's \"text-embedding-3-small\" model\n", "- Build a semantic search engine that finds similar clothes based on product descriptions\n", "- Use cosine distance to measure similarity between embeddings\n", "- Practice handling embeddings and performing vector similarity search in Python\n", "\n", "### Model Used\n", "Azure OpenAI's **text-embedding-3-small** model for generating high-quality text embeddings."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Install Required Dependencies\n", "\n", "First, let's install the necessary libraries for our semantic search engine."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install openai scipy numpy matplotlib pandas"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Import Required Libraries\n", "\n", "Import all necessary libraries for our implementation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from typing import List, Dict, Tuple\n", "from scipy.spatial.distance import cosine\n", "from openai import AzureOpenAI\n", "import time\n", "import warnings\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries imported successfully!\")\n", "print(f\"NumPy version: {np.__version__}\")\n", "print(f\"Pandas version: {pd.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Setup Azure OpenAI Client\n", "\n", "Configure the Azure OpenAI client with your credentials. Make sure to set your environment variables first."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup Azure OpenAI client\n", "def setup_azure_client():\n", "    \"\"\"Setup Azure OpenAI client with environment variables.\"\"\"\n", "    print(\"🔧 Setting up Azure OpenAI client...\")\n", "    \n", "    # Check for environment variables\n", "    required_vars = [\n", "        \"AZURE_OPENAI_ENDPOINT\",\n", "        \"AZURE_OPENAI_API_KEY\", \n", "        \"AZURE_DEPLOYMENT_NAME\"\n", "    ]\n", "    \n", "    missing_vars = []\n", "    for var in required_vars:\n", "        if not os.getenv(var):\n", "            missing_vars.append(var)\n", "    \n", "    if missing_vars:\n", "        print(\"⚠️  Missing environment variables:\")\n", "        for var in missing_vars:\n", "            print(f\"   - {var}\")\n", "        print(\"\\n💡 Set these environment variables in your system or use:\")\n", "        print(\"   os.environ['AZURE_OPENAI_ENDPOINT'] = 'your-endpoint'\")\n", "        print(\"   os.environ['AZURE_OPENAI_API_KEY'] = 'your-api-key'\")\n", "        print(\"   os.environ['AZURE_DEPLOYMENT_NAME'] = 'text-embedding-3-small'\")\n", "        print(\"\\n🔄 Running in DEMO MODE with mock embeddings...\")\n", "        return None, \"text-embedding-3-small\"\n", "    \n", "    try:\n", "        client = AzureOpenAI(\n", "            api_version=\"2024-07-01-preview\",\n", "            azure_endpoint=os.getenv(\"AZURE_OPENAI_ENDPOINT\"),\n", "            api_key=os.getenv(\"AZURE_OPENAI_API_KEY\")\n", "        )\n", "        deployment_name = os.getenv(\"AZURE_DEPLOYMENT_NAME\", \"text-embedding-3-small\")\n", "        print(\"✅ Azure OpenAI client initialized successfully!\")\n", "        return client, deployment_name\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error setting up Azure OpenAI client: {e}\")\n", "        print(\"🔄 Running in DEMO MODE with mock embeddings...\")\n", "        return None, \"text-embedding-3-small\"\n", "\n", "# Initialize client\n", "client, deployment_name = setup_azure_client()\n", "\n", "# Uncomment and set these if you haven't set environment variables\n", "# os.environ['AZURE_OPENAI_ENDPOINT'] = 'your-endpoint-here'\n", "# os.environ['AZURE_OPENAI_API_KEY'] = 'your-api-key-here'\n", "# os.environ['AZURE_DEPLOYMENT_NAME'] = 'text-embedding-3-small'\n", "# client, deployment_name = setup_azure_client()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Create Sample Product Dataset\n", "\n", "Let's create a comprehensive dataset of clothing products with detailed descriptions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample product data\n", "products = [\n", "    {\n", "        \"id\": 1,\n", "        \"title\": \"Classic Blue Jeans\",\n", "        \"short_description\": \"Comfortable blue denim jeans with a relaxed fit, perfect for casual wear.\",\n", "        \"price\": 49.99,\n", "        \"category\": \"Jeans\"\n", "    },\n", "    {\n", "        \"id\": 2,\n", "        \"title\": \"Red Hoodie\",\n", "        \"short_description\": \"Cozy red hoodie made from organic cotton, ideal for cool weather.\",\n", "        \"price\": 39.99,\n", "        \"category\": \"Hoodies\"\n", "    },\n", "    {\n", "        \"id\": 3,\n", "        \"title\": \"Black Leather Jacket\",\n", "        \"short_description\": \"Stylish black leather jacket with a slim fit design, perfect for evening wear.\",\n", "        \"price\": 120.00,\n", "        \"category\": \"Jackets\"\n", "    },\n", "    {\n", "        \"id\": 4,\n", "        \"title\": \"White Cotton T-Shirt\",\n", "        \"short_description\": \"Basic white cotton t-shirt with a comfortable fit, essential wardrobe staple.\",\n", "        \"price\": 19.99,\n", "        \"category\": \"T-Shirts\"\n", "    },\n", "    {\n", "        \"id\": 5,\n", "        \"title\": \"Navy Blue Blazer\",\n", "        \"short_description\": \"Professional navy blue blazer made from wool blend, perfect for business meetings.\",\n", "        \"price\": 89.99,\n", "        \"category\": \"Blazers\"\n", "    },\n", "    {\n", "        \"id\": 6,\n", "        \"title\": \"Gray Sweatpants\",\n", "        \"short_description\": \"Comfortable gray sweatpants made from soft fleece material, great for lounging.\",\n", "        \"price\": 29.99,\n", "        \"category\": \"Pants\"\n", "    },\n", "    {\n", "        \"id\": 7,\n", "        \"title\": \"Floral Summer Dress\",\n", "        \"short_description\": \"Light and airy floral summer dress made from breathable cotton, perfect for warm days.\",\n", "        \"price\": 59.99,\n", "        \"category\": \"Dresses\"\n", "    },\n", "    {\n", "        \"id\": 8,\n", "        \"title\": \"Denim Jacket\",\n", "        \"short_description\": \"Classic denim jacket with vintage wash, versatile piece for layering.\",\n", "        \"price\": 69.99,\n", "        \"category\": \"Jackets\"\n", "    },\n", "    {\n", "        \"id\": 9,\n", "        \"title\": \"Striped Long Sleeve Shirt\",\n", "        \"short_description\": \"Navy and white striped long sleeve shirt, casual yet polished look.\",\n", "        \"price\": 34.99,\n", "        \"category\": \"Shirts\"\n", "    },\n", "    {\n", "        \"id\": 10,\n", "        \"title\": \"Wool Winter Coat\",\n", "        \"short_description\": \"Warm wool winter coat with insulated lining, essential for cold weather protection.\",\n", "        \"price\": 149.99,\n", "        \"category\": \"Coats\"\n", "    },\n", "    {\n", "        \"id\": 11,\n", "        \"title\": \"Athletic Shorts\",\n", "        \"short_description\": \"Moisture-wicking athletic shorts with built-in compression, perfect for workouts.\",\n", "        \"price\": 24.99,\n", "        \"category\": \"Shorts\"\n", "    },\n", "    {\n", "        \"id\": 12,\n", "        \"title\": \"Cashmere Sweater\",\n", "        \"short_description\": \"Luxurious cashmere sweater in cream color, soft and elegant for special occasions.\",\n", "        \"price\": 199.99,\n", "        \"category\": \"Sweaters\"\n", "    }\n", "]\n", "\n", "# Display products as DataFrame\n", "df = pd.DataFrame(products)\n", "print(f\"📦 Loaded {len(products)} products\")\n", "print(\"\\n📊 Product Overview:\")\n", "display(df[['title', 'category', 'price']].head(10))\n", "\n", "# Category distribution\n", "category_counts = df['category'].value_counts()\n", "print(f\"\\n📈 Products by Category:\")\n", "for category, count in category_counts.items():\n", "    print(f\"   {category}: {count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Function to Get Embeddings from Azure OpenAI\n", "\n", "Create a function to generate embeddings using the text-embedding-3-small model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_embedding(text: str) -> List[float]:\n", "    \"\"\"\n", "    Get embedding for a given text using Azure OpenAI.\n", "    \n", "    Args:\n", "        text (str): Text to embed\n", "        \n", "    Returns:\n", "        List[float]: Embedding vector\n", "    \"\"\"\n", "    if client is None:\n", "        # Return mock embedding for demo mode\n", "        np.random.seed(hash(text) % 2**32)  # Consistent mock embeddings\n", "        return np.random.normal(0, 1, 1536).tolist()  # text-embedding-3-small dimension\n", "    \n", "    try:\n", "        response = client.embeddings.create(\n", "            model=deployment_name,\n", "            input=text\n", "        )\n", "        embedding = response.data[0].embedding\n", "        return embedding\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error getting embedding: {e}\")\n", "        # Fallback to mock embedding\n", "        np.random.seed(hash(text) % 2**32)\n", "        return np.random.normal(0, 1, 1536).tolist()\n", "\n", "# Test the embedding function\n", "test_text = \"comfortable cotton t-shirt\"\n", "test_embedding = get_embedding(test_text)\n", "print(f\"✅ Embedding function working!\")\n", "print(f\"   Text: '{test_text}'\")\n", "print(f\"   Embedding dimension: {len(test_embedding)}\")\n", "print(f\"   First 5 values: {test_embedding[:5]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Generate Embeddings for All Product Descriptions\n", "\n", "Now let's generate embeddings for all our product descriptions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate embeddings for all products\n", "print(\"🔄 Generating embeddings for product descriptions...\")\n", "\n", "for i, product in enumerate(products):\n", "    print(f\"   Processing product {i+1}/{len(products)}: {product['title']}\")\n", "    \n", "    # Use short_description for embedding\n", "    embedding = get_embedding(product[\"short_description\"])\n", "    product[\"embedding\"] = embedding\n", "    \n", "    # Add small delay to avoid rate limiting (only if using real API)\n", "    if client is not None:\n", "        time.sleep(0.1)\n", "\n", "print(\"✅ All product embeddings generated!\")\n", "\n", "# Verify embeddings are added\n", "sample_product = products[0]\n", "print(f\"\\n📊 Sample embedding info:\")\n", "print(f\"   Product: {sample_product['title']}\")\n", "print(f\"   Embedding dimension: {len(sample_product['embedding'])}\")\n", "print(f\"   Embedding type: {type(sample_product['embedding'])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Implement Cosine Similarity Function\n", "\n", "Create a function to calculate cosine similarity between embeddings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def similarity_score(vec1: List[float], vec2: List[float]) -> float:\n", "    \"\"\"\n", "    Calculate cosine similarity between two vectors.\n", "    \n", "    Args:\n", "        vec1, vec2: Embedding vectors\n", "        \n", "    Returns:\n", "        float: Similarity score (higher = more similar)\n", "    \"\"\"\n", "    # Cosine function returns distance, so we use 1 - distance for similarity\n", "    return 1 - cosine(vec1, vec2)\n", "\n", "# Test similarity function\n", "vec1 = products[0]['embedding']  # Blue jeans\n", "vec2 = products[7]['embedding']  # Denim jacket\n", "vec3 = products[4]['embedding']  # Navy blazer\n", "\n", "sim_jeans_denim = similarity_score(vec1, vec2)\n", "sim_jeans_blazer = similarity_score(vec1, vec3)\n", "\n", "print(\"🧪 Testing similarity function:\")\n", "print(f\"   <PERSON> Jeans ↔ Denim Jacket: {sim_jeans_denim:.4f}\")\n", "print(f\"   Blue Jeans ↔ Navy Blazer: {sim_jeans_blazer:.4f}\")\n", "print(f\"   Expected: Jeans should be more similar to denim jacket than blazer\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Implement Search Function\n", "\n", "Create the main search function that finds similar products based on a query."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def search_products(query: str, top_n: int = 5) -> List[Tuple[Dict, float]]:\n", "    \"\"\"\n", "    Search for products similar to the query.\n", "    \n", "    Args:\n", "        query (str): Search query\n", "        top_n (int): Number of top results to return\n", "        \n", "    Returns:\n", "        List[Tuple[Dict, float]]: List of (product, similarity_score) tuples\n", "    \"\"\"\n", "    print(f\"🔍 Searching for: '{query}'\")\n", "    \n", "    # Get embedding for the query\n", "    query_embedding = get_embedding(query)\n", "    \n", "    # Calculate similarities\n", "    similarities = []\n", "    for product in products:\n", "        similarity = similarity_score(query_embedding, product[\"embedding\"])\n", "        similarities.append((product, similarity))\n", "    \n", "    # Sort by similarity (descending)\n", "    similarities.sort(key=lambda x: x[1], reverse=True)\n", "    \n", "    return similarities[:top_n]\n", "\n", "def display_search_results(results: List[Tuple[Dict, float]], query: str):\n", "    \"\"\"\n", "    Display search results in a formatted way.\n", "    \n", "    Args:\n", "        results: List of (product, similarity_score) tuples\n", "        query: Original search query\n", "    \"\"\"\n", "    print(f\"\\n🎯 Top {len(results)} matching products for query: '{query}'\")\n", "    print(\"=\" * 80)\n", "    \n", "    for i, (product, score) in enumerate(results, 1):\n", "        print(f\"\\n{i}. {product['title']}\")\n", "        print(f\"   Category: {product['category']}\")\n", "        print(f\"   Price: ${product['price']:.2f}\")\n", "        print(f\"   Description: {product['short_description']}\")\n", "        print(f\"   Similarity Score: {score:.4f}\")\n", "        print(\"-\" * 60)\n", "\n", "print(\"✅ Search functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 9: Test the Semantic Search Engine\n", "\n", "Let's test our search engine with various queries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Query 1: Warm cotton sweatshirt\n", "query1 = \"warm cotton sweatshirt\"\n", "results1 = search_products(query1, top_n=3)\n", "display_search_results(results1, query1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Query 2: Professional business attire\n", "query2 = \"professional business attire\"\n", "results2 = search_products(query2, top_n=3)\n", "display_search_results(results2, query2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Query 3: Casual denim clothing\n", "query3 = \"casual denim clothing\"\n", "results3 = search_products(query3, top_n=3)\n", "display_search_results(results3, query3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Query 4: Comfortable workout gear\n", "query4 = \"comfortable workout gear\"\n", "results4 = search_products(query4, top_n=3)\n", "display_search_results(results4, query4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Query 5: Elegant evening wear\n", "query5 = \"elegant evening wear\"\n", "results5 = search_products(query5, top_n=3)\n", "display_search_results(results5, query5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 10: Analyze Search Results and Embeddings\n", "\n", "Let's analyze the quality of our search results and visualize embedding similarities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create similarity matrix for all products\n", "def create_similarity_matrix():\n", "    \"\"\"Create a similarity matrix for all products.\"\"\"\n", "    n_products = len(products)\n", "    similarity_matrix = np.zeros((n_products, n_products))\n", "    \n", "    for i in range(n_products):\n", "        for j in range(n_products):\n", "            if i == j:\n", "                similarity_matrix[i][j] = 1.0\n", "            else:\n", "                sim = similarity_score(products[i]['embedding'], products[j]['embedding'])\n", "                similarity_matrix[i][j] = sim\n", "    \n", "    return similarity_matrix\n", "\n", "# Generate similarity matrix\n", "print(\"🔄 Creating product similarity matrix...\")\n", "sim_matrix = create_similarity_matrix()\n", "print(\"✅ Similarity matrix created!\")\n", "\n", "# Visualize similarity matrix\n", "plt.figure(figsize=(12, 10))\n", "plt.imshow(sim_matrix, cmap='viridis', aspect='auto')\n", "plt.colorbar(label='Cosine Similarity')\n", "plt.title('Product Similarity Matrix')\n", "plt.xlabel('Product Index')\n", "plt.ylabel('Product Index')\n", "\n", "# Add product titles as labels\n", "product_titles = [p['title'][:15] + '...' if len(p['title']) > 15 else p['title'] for p in products]\n", "plt.xticks(range(len(products)), product_titles, rotation=45, ha='right')\n", "plt.yticks(range(len(products)), product_titles)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Find most similar product pairs\n", "print(\"\\n🔍 Most Similar Product Pairs:\")\n", "similarities_flat = []\n", "for i in range(len(products)):\n", "    for j in range(i+1, len(products)):\n", "        similarities_flat.append((i, j, sim_matrix[i][j]))\n", "\n", "similarities_flat.sort(key=lambda x: x[2], reverse=True)\n", "\n", "for i, (idx1, idx2, sim) in enumerate(similarities_flat[:5]):\n", "    print(f\"{i+1}. {products[idx1]['title']} ↔ {products[idx2]['title']}\")\n", "    print(f\"   Similarity: {sim:.4f}\")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 11: Interactive Search Interface\n", "\n", "Create an interactive interface for testing different search queries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive search function\n", "def interactive_search():\n", "    \"\"\"Interactive search interface for <PERSON><PERSON><PERSON> notebook.\"\"\"\n", "    print(\"🚀 Interactive Semantic Search!\")\n", "    print(\"Enter your search queries below. Type 'quit' to stop.\")\n", "    print(\"\\nExample queries:\")\n", "    print(\"- 'warm winter clothing'\")\n", "    print(\"- 'casual everyday wear'\")\n", "    print(\"- 'formal office attire'\")\n", "    print(\"- 'comfortable loungewear'\")\n", "    \n", "    while True:\n", "        query = input(\"\\n🔍 Enter search query (or 'quit'): \").strip()\n", "        \n", "        if query.lower() == 'quit':\n", "            print(\"👋 Search session ended!\")\n", "            break\n", "        elif not query:\n", "            print(\"⚠️  Please enter a search query.\")\n", "            continue\n", "        \n", "        try:\n", "            results = search_products(query, top_n=3)\n", "            display_search_results(results, query)\n", "        except Exception as e:\n", "            print(f\"❌ Error during search: {e}\")\n", "\n", "# Note: Uncomment the line below to start interactive search\n", "# interactive_search()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 12: Performance Analysis and Insights\n", "\n", "Let's analyze the performance and characteristics of our semantic search engine."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze search performance\n", "def analyze_search_performance():\n", "    \"\"\"Analyze the performance of our search engine.\"\"\"\n", "    print(\"📊 Search Engine Performance Analysis\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Test queries with expected categories\n", "    test_cases = [\n", "        (\"warm winter jacket\", [\"Jackets\", \"Coats\"]),\n", "        (\"casual cotton shirt\", [\"T-Shirts\", \"Shirts\"]),\n", "        (\"professional business wear\", [\"Blazers\", \"Shirts\"]),\n", "        (\"comfortable workout clothes\", [\"Shorts\", \"T-Shirts\"]),\n", "        (\"elegant formal dress\", [\"Dresses\", \"Blazers\"])\n", "    ]\n", "    \n", "    correct_predictions = 0\n", "    total_predictions = 0\n", "    \n", "    for query, expected_categories in test_cases:\n", "        results = search_products(query, top_n=3)\n", "        \n", "        print(f\"\\nQuery: '{query}'\")\n", "        print(f\"Expected categories: {expected_categories}\")\n", "        print(\"Top 3 results:\")\n", "        \n", "        for i, (product, score) in enumerate(results, 1):\n", "            category_match = product['category'] in expected_categories\n", "            match_symbol = \"✅\" if category_match else \"❌\"\n", "            print(f\"  {i}. {product['title']} ({product['category']}) - {score:.3f} {match_symbol}\")\n", "            \n", "            if category_match:\n", "                correct_predictions += 1\n", "            total_predictions += 1\n", "    \n", "    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0\n", "    print(f\"\\n📈 Overall Accuracy: {accuracy:.2%} ({correct_predictions}/{total_predictions})\")\n", "    \n", "    return accuracy\n", "\n", "# Run performance analysis\n", "accuracy = analyze_search_performance()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 13: <PERSON><PERSON><PERSON> and Key Insights\n", "\n", "Let's summarize what we've learned and the key insights from our semantic search engine."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary of results\n", "print(\"🎯 Assignment 8 Summary: Semantic Search Engine\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n✅ Achievements:\")\n", "print(\"   • Successfully integrated Azure OpenAI text-embedding-3-small model\")\n", "print(\"   • Built a semantic search engine for clothing products\")\n", "print(\"   • Implemented cosine similarity for vector comparison\")\n", "print(\"   • Created interactive search functionality\")\n", "print(\"   • Analyzed search performance and accuracy\")\n", "\n", "print(\"\\n📊 Technical Specifications:\")\n", "print(f\"   • Embedding Model: {deployment_name}\")\n", "print(f\"   • Embedding Dimension: 1536\")\n", "print(f\"   • Number of Products: {len(products)}\")\n", "print(f\"   • Similarity Metric: Cosine Similarity\")\n", "print(f\"   • Search Accuracy: {accuracy:.1%}\")\n", "\n", "print(\"\\n🔍 Key Insights:\")\n", "print(\"   • Semantic search captures meaning beyond keyword matching\")\n", "print(\"   • Text embeddings enable similarity comparison in vector space\")\n", "print(\"   • Cosine similarity effectively measures semantic relatedness\")\n", "print(\"   • Quality of product descriptions directly impacts search accuracy\")\n", "\n", "print(\"\\n🚀 Potential Improvements:\")\n", "print(\"   • Add more diverse product descriptions\")\n", "print(\"   • Implement category-weighted similarity scoring\")\n", "print(\"   • Add price range filtering\")\n", "print(\"   • Include user preference learning\")\n", "print(\"   • Implement batch embedding processing for efficiency\")\n", "\n", "print(\"\\n🎉 Assignment 8 Complete!\")\n", "print(\"You have successfully built a semantic search engine using Azure OpenAI embeddings!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}