# Setup Guide - Assignment 8: Semantic Search Engine

## Quick Start (10 minutes)

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Set Azure OpenAI Credentials
```bash
export AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com/"
export AZURE_OPENAI_API_KEY="your-api-key-here"
export AZURE_DEPLOYMENT_NAME="text-embedding-3-small"
```

### 3. Run Demo
```bash
python semantic_search_engine.py
```

---

## Detailed Setup Instructions

### Prerequisites

#### Azure OpenAI Access
1. **Azure Subscription**: Active Azure subscription
2. **OpenAI Resource**: Azure OpenAI resource created
3. **Model Deployment**: text-embedding-3-small model deployed
4. **API Access**: Endpoint URL and API key

#### System Requirements
- Python 3.8+
- 4GB+ RAM
- Internet connection
- Command line access

### Step 1: Azure OpenAI Setup

#### Create Azure OpenAI Resource
1. Go to [Azure Portal](https://portal.azure.com)
2. Create new resource → AI + Machine Learning → Azure OpenAI
3. Fill in resource details:
   - Subscription: Your subscription
   - Resource group: Create new or use existing
   - Region: Choose available region
   - Name: Unique resource name
   - Pricing tier: Standard S0

#### Deploy text-embedding-3-small Model
1. Go to Azure OpenAI Studio
2. Navigate to Deployments
3. Create new deployment:
   - Model: text-embedding-3-small
   - Deployment name: text-embedding-3-small
   - Version: Latest available

#### Get API Credentials
1. In Azure Portal, go to your OpenAI resource
2. Navigate to Keys and Endpoint
3. Copy:
   - Endpoint URL
   - API Key (Key 1 or Key 2)
   - Deployment name

### Step 2: Environment Setup

#### Option A: Environment Variables (Recommended)
```bash
# Linux/Mac
export AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com/"
export AZURE_OPENAI_API_KEY="your-32-character-api-key"
export AZURE_DEPLOYMENT_NAME="text-embedding-3-small"

# Windows Command Prompt
set AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
set AZURE_OPENAI_API_KEY=your-32-character-api-key
set AZURE_DEPLOYMENT_NAME=text-embedding-3-small

# Windows PowerShell
$env:AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com/"
$env:AZURE_OPENAI_API_KEY="your-32-character-api-key"
$env:AZURE_DEPLOYMENT_NAME="text-embedding-3-small"
```

#### Option B: .env File
Create a `.env` file in the assignment directory:
```bash
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-32-character-api-key
AZURE_DEPLOYMENT_NAME=text-embedding-3-small
```

#### Option C: Direct Code Modification
In the Python files, uncomment and modify:
```python
os.environ['AZURE_OPENAI_ENDPOINT'] = 'https://your-resource.openai.azure.com/'
os.environ['AZURE_OPENAI_API_KEY'] = 'your-32-character-api-key'
os.environ['AZURE_DEPLOYMENT_NAME'] = 'text-embedding-3-small'
```

### Step 3: Install Python Dependencies

#### Using pip (Recommended)
```bash
# Navigate to assignment directory
cd assignment/assignment8

# Install all dependencies
pip install -r requirements.txt

# Verify installation
python -c "import openai, numpy, scipy; print('✅ All packages installed!')"
```

#### Manual Installation
```bash
# Core packages
pip install openai>=1.0.0
pip install numpy scipy pandas
pip install matplotlib seaborn
pip install jupyter ipython
```

### Step 4: Verification

#### Test 1: Check Environment Variables
```bash
python -c "
import os
print('Endpoint:', os.getenv('AZURE_OPENAI_ENDPOINT'))
print('API Key:', os.getenv('AZURE_OPENAI_API_KEY')[:10] + '...' if os.getenv('AZURE_OPENAI_API_KEY') else 'Not set')
print('Deployment:', os.getenv('AZURE_DEPLOYMENT_NAME'))
"
```

#### Test 2: Test API Connection
```python
from openai import AzureOpenAI
import os

client = AzureOpenAI(
    api_version="2024-07-01-preview",
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    api_key=os.getenv("AZURE_OPENAI_API_KEY")
)

# Test embedding
response = client.embeddings.create(
    model=os.getenv("AZURE_DEPLOYMENT_NAME"),
    input="test text"
)
print(f"✅ API working! Embedding dimension: {len(response.data[0].embedding)}")
```

#### Test 3: Run Demo Script
```bash
python semantic_search_engine.py
```

Expected output:
- Azure OpenAI client initialization
- Product embedding generation
- Demo search results
- Interactive search prompt

---

## Troubleshooting

### Common Issues

#### 1. "Missing environment variables"
**Problem**: Environment variables not set
**Solution**:
```bash
# Check current variables
env | grep AZURE

# Set missing variables
export AZURE_OPENAI_ENDPOINT="your-endpoint"
export AZURE_OPENAI_API_KEY="your-key"
export AZURE_DEPLOYMENT_NAME="text-embedding-3-small"
```

#### 2. "Authentication failed"
**Problem**: Invalid API credentials
**Solutions**:
- Verify endpoint URL format (must end with `/`)
- Check API key (32 characters, no spaces)
- Ensure resource is active in Azure Portal
- Try regenerating API key

#### 3. "Model not found"
**Problem**: Deployment name incorrect
**Solutions**:
- Check deployment name in Azure OpenAI Studio
- Ensure model is deployed and active
- Verify deployment name matches environment variable

#### 4. "Rate limit exceeded"
**Problem**: Too many API calls
**Solutions**:
- Add delays between calls: `time.sleep(0.1)`
- Reduce batch sizes
- Check Azure OpenAI quotas

#### 5. "Import errors"
**Problem**: Missing Python packages
**Solutions**:
```bash
# Update pip
pip install --upgrade pip

# Reinstall packages
pip install --force-reinstall openai numpy scipy

# Check Python version
python --version  # Should be 3.8+
```

### Demo Mode Fallback

If Azure OpenAI is not available, the system automatically runs in **Demo Mode**:
- Uses mock embeddings (consistent random vectors)
- Maintains relative similarity relationships
- Allows full functionality testing
- Perfect for learning without API costs

### Performance Optimization

#### For Better Performance:
1. **Cache Embeddings**: Store generated embeddings
2. **Batch Processing**: Process multiple texts together
3. **Async Calls**: Use async/await for concurrent requests
4. **Local Storage**: Save embeddings to avoid regeneration

#### Memory Management:
```python
# Clear large variables when done
del similarity_matrix
import gc
gc.collect()
```

---

## Alternative Setups

### Using Docker
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "semantic_search_engine.py"]
```

### Using Conda
```bash
# Create environment
conda create -n semantic-search python=3.9
conda activate semantic-search

# Install packages
conda install numpy scipy pandas matplotlib
pip install openai jupyter
```

### Using Virtual Environment
```bash
# Create virtual environment
python -m venv semantic-search-env

# Activate (Linux/Mac)
source semantic-search-env/bin/activate

# Activate (Windows)
semantic-search-env\Scripts\activate

# Install packages
pip install -r requirements.txt
```

---

## Success Indicators

✅ **Setup Complete** when:
- All environment variables are set
- Python packages install without errors
- API connection test succeeds
- Demo script runs and shows search results

✅ **Ready for Assignment** when:
- Interactive search accepts queries
- Similarity scores are calculated
- Product results are ranked correctly
- Jupyter notebook opens and runs all cells

---

## Getting Help

### If you encounter issues:

1. **Check Error Messages**: Read the full error message
2. **Verify Credentials**: Double-check all Azure OpenAI settings
3. **Test Components**: Test each part separately
4. **Use Demo Mode**: Run without API to test logic
5. **Check Documentation**: Review Azure OpenAI docs

### Common Solutions:
```bash
# Reset environment
unset AZURE_OPENAI_ENDPOINT AZURE_OPENAI_API_KEY AZURE_DEPLOYMENT_NAME

# Clear Python cache
find . -name "*.pyc" -delete
find . -name "__pycache__" -delete

# Restart Python/Jupyter
# Exit and restart your Python session or Jupyter kernel
```

---

**Setup Complete!** 🎉

You're now ready to build and test your semantic search engine. Start with the demo script to verify everything works, then explore the Jupyter notebook for detailed implementation.
