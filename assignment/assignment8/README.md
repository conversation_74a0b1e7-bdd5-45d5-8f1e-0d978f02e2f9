# Assignment 8: Semantic Search Engine with Text Embeddings

## Overview

This assignment demonstrates how to build a semantic search engine for clothing products using Azure OpenAI's text-embedding-3-small model. The system converts product descriptions and user queries into high-dimensional vectors (embeddings) and uses cosine similarity to find the most relevant products.

## Objectives

- ✅ Learn to generate text embeddings using Azure OpenAI's "text-embedding-3-small" model
- ✅ Build a semantic search engine that finds similar clothes based on product descriptions
- ✅ Use cosine distance to measure similarity between embeddings
- ✅ Practice handling embeddings and performing vector similarity search in Python
- ✅ Understand the advantages of semantic search over keyword matching

## Key Concepts

### Semantic Search vs. Keyword Search
- **Keyword Search**: Matches exact words or phrases
- **Semantic Search**: Understands meaning and context, finding conceptually similar items

### Text Embeddings
- High-dimensional vector representations of text
- Capture semantic meaning and relationships
- Enable mathematical operations on text data

### Cosine Similarity
- Measures the cosine of the angle between two vectors
- Values range from -1 to 1 (higher = more similar)
- Effective for comparing text embeddings

## Files Included

1. **`semantic_search_engine.py`** - Complete Python script with interactive search
2. **`semantic_search_notebook.ipynb`** - Jupyter notebook with step-by-step implementation
3. **`requirements.txt`** - Required Python packages
4. **`README.md`** - This documentation file
5. **`setup_guide.md`** - Detailed setup instructions
6. **`demo_script.py`** - Quick demonstration script

## Prerequisites

### Azure OpenAI Setup
1. **Azure OpenAI Resource**: You need access to Azure OpenAI Service
2. **API Credentials**: Endpoint URL, API key, and deployment name
3. **Model Deployment**: text-embedding-3-small model deployed in your resource

### System Requirements
- Python 3.8 or higher
- 4GB+ RAM available
- Internet connection for API calls
- Azure OpenAI API access

## Installation

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Set Environment Variables
```bash
# Option A: Set in your shell
export AZURE_OPENAI_ENDPOINT="your-endpoint-url"
export AZURE_OPENAI_API_KEY="your-api-key"
export AZURE_DEPLOYMENT_NAME="text-embedding-3-small"

# Option B: Create a .env file
echo "AZURE_OPENAI_ENDPOINT=your-endpoint-url" > .env
echo "AZURE_OPENAI_API_KEY=your-api-key" >> .env
echo "AZURE_DEPLOYMENT_NAME=text-embedding-3-small" >> .env
```

### 3. Verify Setup
```bash
python demo_script.py
```

## Usage

### Option 1: Interactive Python Script
```bash
python semantic_search_engine.py
```

Features:
- Automatic product embedding generation
- Interactive search interface
- Demo searches with predefined queries
- Performance analysis and visualization

### Option 2: Jupyter Notebook
```bash
jupyter notebook semantic_search_notebook.ipynb
```

Features:
- Step-by-step implementation walkthrough
- Interactive code cells
- Visualization of similarity matrices
- Performance analysis charts

## Sample Product Dataset

The system includes 12 sample clothing products:

| Category | Products |
|----------|----------|
| Jeans | Classic Blue Jeans |
| Hoodies | Red Hoodie |
| Jackets | Black Leather Jacket, Denim Jacket |
| T-Shirts | White Cotton T-Shirt |
| Blazers | Navy Blue Blazer |
| Pants | Gray Sweatpants |
| Dresses | Floral Summer Dress |
| Shirts | Striped Long Sleeve Shirt |
| Coats | Wool Winter Coat |
| Shorts | Athletic Shorts |
| Sweaters | Cashmere Sweater |

## Example Searches

### Query: "warm cotton sweatshirt"
**Expected Results:**
1. Red Hoodie (organic cotton, cozy)
2. Gray Sweatpants (soft fleece material)
3. Cashmere Sweater (soft and warm)

### Query: "professional business attire"
**Expected Results:**
1. Navy Blue Blazer (business meetings)
2. Striped Long Sleeve Shirt (polished look)
3. Black Leather Jacket (evening wear)

### Query: "casual denim clothing"
**Expected Results:**
1. Classic Blue Jeans (casual wear)
2. Denim Jacket (versatile layering)
3. Black Leather Jacket (casual style)

## Technical Implementation

### 1. Embedding Generation
```python
def get_embedding(text: str) -> List[float]:
    response = client.embeddings.create(
        model="text-embedding-3-small",
        input=text
    )
    return response.data[0].embedding
```

### 2. Similarity Calculation
```python
def similarity_score(vec1: List[float], vec2: List[float]) -> float:
    return 1 - cosine(vec1, vec2)  # Convert distance to similarity
```

### 3. Search Function
```python
def search_products(query: str, top_n: int = 5):
    query_embedding = get_embedding(query)
    similarities = []
    
    for product in products:
        similarity = similarity_score(query_embedding, product["embedding"])
        similarities.append((product, similarity))
    
    return sorted(similarities, key=lambda x: x[1], reverse=True)[:top_n]
```

## Performance Metrics

The system tracks several performance indicators:

- **Search Accuracy**: Percentage of relevant results in top-N
- **Response Time**: Time to process queries and return results
- **Embedding Quality**: Consistency of similar product groupings
- **Category Relevance**: How well results match expected product categories

## Demo Mode

If Azure OpenAI credentials are not available, the system runs in **Demo Mode**:
- Uses mock embeddings with consistent random generation
- Maintains relative similarity relationships
- Allows testing of search functionality
- Perfect for learning and development

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify Azure OpenAI credentials
   - Check endpoint URL format
   - Ensure API key is valid

2. **Rate Limiting**
   - Add delays between API calls
   - Implement batch processing
   - Use caching for repeated queries

3. **Poor Search Results**
   - Improve product descriptions
   - Add more diverse products
   - Fine-tune similarity thresholds

### Error Messages

- `"Missing environment variables"` → Set Azure OpenAI credentials
- `"Error getting embedding"` → Check API connectivity
- `"Model not found"` → Verify deployment name

## Extensions and Improvements

### Immediate Enhancements
1. **Larger Product Database**: Add more diverse products
2. **Category Filtering**: Filter results by product category
3. **Price Range Filtering**: Include price-based filtering
4. **Batch Processing**: Process multiple queries efficiently

### Advanced Features
1. **User Preference Learning**: Adapt to user search patterns
2. **Multi-modal Search**: Include image embeddings
3. **Real-time Updates**: Dynamic product catalog updates
4. **A/B Testing**: Compare different embedding models

### Production Considerations
1. **Caching Strategy**: Cache embeddings and frequent queries
2. **Database Integration**: Store embeddings in vector database
3. **API Rate Limiting**: Implement proper rate limiting
4. **Monitoring**: Track search performance and user satisfaction

## Learning Outcomes

After completing this assignment, you will understand:

1. **Text Embeddings**: How to generate and use text embeddings
2. **Vector Similarity**: Mathematical concepts behind similarity search
3. **API Integration**: Working with Azure OpenAI services
4. **Search Systems**: Building practical search applications
5. **Performance Analysis**: Evaluating search system effectiveness

## Next Steps

1. **Experiment**: Try different queries and analyze results
2. **Extend**: Add more products or categories
3. **Optimize**: Improve search accuracy and performance
4. **Deploy**: Consider production deployment strategies
5. **Explore**: Investigate other embedding models and techniques

## References

- [Azure OpenAI Service Documentation](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/)
- [OpenAI Embeddings Guide](https://platform.openai.com/docs/guides/embeddings)
- [Cosine Similarity Explanation](https://en.wikipedia.org/wiki/Cosine_similarity)
- [Vector Search Best Practices](https://www.pinecone.io/learn/vector-search/)

---

**Assignment 8 Complete** ✅

This implementation provides a comprehensive introduction to semantic search using modern embedding techniques, demonstrating both theoretical concepts and practical applications in e-commerce search systems.
