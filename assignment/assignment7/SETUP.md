# Setup Guide - Assignment 7: Text-to-Speech with Hugging Face

## Quick Start (5 minutes)

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run Quick Demo
```bash
python quick_demo.py
```

### 3. Open Jupyter Notebook
```bash
jupyter notebook tts_inference.ipynb
```

---

## Detailed Setup Instructions

### Prerequisites
- Python 3.7+
- pip package manager
- 4GB+ RAM available
- Internet connection (for model downloads)

### Step-by-Step Installation

#### Option A: Using requirements.txt (Recommended)
```bash
# Navigate to assignment directory
cd assignment/assignment7

# Install all dependencies at once
pip install -r requirements.txt

# Verify installation
python -c "import torch, transformers; print('✅ Installation successful!')"
```

#### Option B: Manual Installation
```bash
# Core ML libraries
pip install torch torchaudio

# Hugging Face libraries
pip install transformers datasets

# Audio processing
pip install soundfile librosa

# Visualization
pip install matplotlib numpy scipy

# Jupyter (optional)
pip install jupyter ipython
```

### Verification

#### Test 1: Quick Demo
```bash
python quick_demo.py
```
Expected output: Audio file `quick_demo_output.wav` created

#### Test 2: Full Demo
```bash
python tts_inference.py
```
Expected output: Multiple audio files in `tts_outputs/` directory

#### Test 3: Jupyter Notebook
```bash
jupyter notebook tts_inference.ipynb
```
Expected: Interactive notebook opens in browser

---

## Troubleshooting

### Common Issues

#### 1. "ModuleNotFoundError"
```bash
# Solution: Install missing package
pip install [package_name]
```

#### 2. "Out of Memory" Error
```bash
# Solution: Close other applications or use CPU-only mode
# Add this to your Python code:
import os
os.environ['CUDA_VISIBLE_DEVICES'] = ''  # Force CPU usage
```

#### 3. Model Download Fails
```bash
# Solution: Check internet connection and try again
# Models will be cached after first successful download
```

#### 4. Audio Playback Issues
- Use external audio player for WAV files
- Check system audio drivers
- Verify file permissions

### System Requirements

#### Minimum:
- Python 3.7+
- 4GB RAM
- 2GB free disk space
- Internet connection

#### Recommended:
- Python 3.8+
- 8GB RAM
- GPU with CUDA support (optional)
- 5GB free disk space

---

## File Structure

```
assignment7/
├── README.md              # Complete documentation
├── SETUP.md              # This setup guide
├── requirements.txt      # Python dependencies
├── tts_inference.ipynb   # Jupyter notebook (main assignment)
├── tts_inference.py      # Python script version
└── quick_demo.py         # Quick test script
```

---

## Getting Help

### If you encounter issues:

1. **Check error messages** - They usually indicate the specific problem
2. **Verify dependencies** - Run `python quick_demo.py` to check installation
3. **Check system resources** - Ensure sufficient RAM and disk space
4. **Try CPU-only mode** - If GPU issues occur
5. **Restart Python** - Clear any cached imports

### Common Solutions:

```bash
# Update pip
pip install --upgrade pip

# Clear pip cache
pip cache purge

# Reinstall packages
pip uninstall torch transformers
pip install torch transformers

# Use CPU-only PyTorch (if GPU issues)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

---

## Success Indicators

✅ **Installation Complete** when:
- `python quick_demo.py` runs without errors
- Audio file is generated and playable
- All imports work correctly

✅ **Assignment Ready** when:
- Jupyter notebook opens successfully
- All notebook cells run without errors
- Multiple audio files are generated

---

## Next Steps After Setup

1. **Run Quick Demo** - Verify everything works
2. **Open Jupyter Notebook** - Complete the full assignment
3. **Experiment** - Try different text inputs
4. **Explore** - Check out different TTS models

---

**Setup Complete!** 🎉

You're now ready to complete Assignment 7. Start with the Jupyter notebook for the best experience.
