{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Text-to-Speech (TTS) Model Inference with Hugging Face\n", "\n", "## Assignment 7: Clone and Perform Inference with Hugging Face TTS Model\n", "\n", "### Objective\n", "- Learn how to clone a pre-trained Text-to-Speech (TTS) model from Hugging Face\n", "- Perform inference to convert input text into spoken audio using the cloned model\n", "- Understand the basics of TTS technology\n", "\n", "### Model Used\n", "We'll use Microsoft's SpeechT5 TTS model, which is a state-of-the-art text-to-speech model available on Hugging Face."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Install Required Dependencies\n", "\n", "First, we need to install the necessary libraries for TTS inference."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install transformers torch torchaudio soundfile datasets IPython numpy"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Import Required Libraries\n", "\n", "Import all the necessary libraries for our TTS implementation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import soundfile as sf\n", "import numpy as np\n", "from transformers import SpeechT5Processor, SpeechT5ForTextToSpeech, SpeechT5HifiGan\n", "from datasets import load_dataset\n", "from IPython.display import Audio, display\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries imported successfully!\")\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Load Pre-trained TTS Model and Processor\n", "\n", "We'll use Microsoft's SpeechT5 model, which is a powerful TTS model that can generate high-quality speech."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the processor and model\n", "print(\"Loading SpeechT5 processor...\")\n", "processor = SpeechT5Processor.from_pretrained(\"microsoft/speecht5_tts\")\n", "\n", "print(\"Loading SpeechT5 TTS model...\")\n", "model = SpeechT5ForTextToSpeech.from_pretrained(\"microsoft/speecht5_tts\")\n", "\n", "print(\"Loading SpeechT5 vocoder (HiFi-GAN)...\")\n", "vocoder = SpeechT5HifiGan.from_pretrained(\"microsoft/speecht5_hifigan\")\n", "\n", "print(\"All models loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: <PERSON><PERSON> Speaker Embeddings\n", "\n", "SpeechT5 requires speaker embeddings to generate speech with specific voice characteristics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load speaker embeddings from CMU ARCTIC dataset\n", "print(\"Loading speaker embeddings...\")\n", "embeddings_dataset = load_dataset(\"Matthijs/cmu-arctic-xvectors\", split=\"validation\")\n", "\n", "# Use the first speaker's embedding\n", "speaker_embeddings = torch.tensor(embeddings_dataset[7306][\"xvector\"]).unsqueeze(0)\n", "print(f\"Speaker embeddings shape: {speaker_embeddings.shape}\")\n", "print(\"Speaker embeddings loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Define Text-to-Speech Function\n", "\n", "Create a function that takes text input and generates audio output."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def text_to_speech(text, save_path=None):\n", "    \"\"\"\n", "    Convert text to speech using SpeechT5 model.\n", "    \n", "    Args:\n", "        text (str): Input text to convert to speech\n", "        save_path (str, optional): Path to save the audio file\n", "    \n", "    Returns:\n", "        numpy.ndarray: Generated audio waveform\n", "    \"\"\"\n", "    print(f\"Converting text to speech: '{text}'\")\n", "    \n", "    # Process the input text\n", "    inputs = processor(text=text, return_tensors=\"pt\")\n", "    \n", "    # Generate speech with no gradient computation (inference mode)\n", "    with torch.no_grad():\n", "        speech = model.generate_speech(inputs[\"input_ids\"], speaker_embeddings, vocoder=vocoder)\n", "    \n", "    # Convert to numpy array\n", "    audio_output = speech.numpy()\n", "    \n", "    # Save audio file if path is provided\n", "    if save_path:\n", "        sf.write(save_path, audio_output, samplerate=16000)\n", "        print(f\"Audio saved to: {save_path}\")\n", "    \n", "    return audio_output\n", "\n", "print(\"Text-to-speech function defined successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Perform TTS Inference\n", "\n", "Now let's test our TTS system with different text inputs."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test with a simple sentence\n", "text1 = \"Hello, this is a demonstration of text to speech using Hugging Face transformers.\"\n", "\n", "print(\"Generating speech for text 1...\")\n", "audio1 = text_to_speech(text1, save_path=\"output1.wav\")\n", "\n", "print(f\"Generated audio shape: {audio1.shape}\")\n", "print(f\"Audio duration: {len(audio1) / 16000:.2f} seconds\")\n", "\n", "# Display audio player in Jupyter\n", "display(Audio(audio1, rate=16000))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test with a longer, more complex sentence\n", "text2 = \"Artificial intelligence and machine learning are revolutionizing the way we interact with technology. Text-to-speech systems like this one enable computers to communicate with humans using natural-sounding voices.\"\n", "\n", "print(\"Generating speech for text 2...\")\n", "audio2 = text_to_speech(text2, save_path=\"output2.wav\")\n", "\n", "print(f\"Generated audio shape: {audio2.shape}\")\n", "print(f\"Audio duration: {len(audio2) / 16000:.2f} seconds\")\n", "\n", "# Display audio player in Jupyter\n", "display(Audio(audio2, rate=16000))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test with a custom sentence (you can modify this)\n", "custom_text = \"Welcome to the world of artificial intelligence and natural language processing!\"\n", "\n", "print(\"Generating speech for custom text...\")\n", "audio_custom = text_to_speech(custom_text, save_path=\"output_custom.wav\")\n", "\n", "print(f\"Generated audio shape: {audio_custom.shape}\")\n", "print(f\"Audio duration: {len(audio_custom) / 16000:.2f} seconds\")\n", "\n", "# Display audio player in Jupyter\n", "display(Audio(audio_custom, rate=16000))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Analyze Model Performance and Audio Quality\n", "\n", "Let's examine the generated audio characteristics and model performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Visualize the waveform of the generated audio\n", "plt.figure(figsize=(12, 6))\n", "\n", "# Plot waveform\n", "plt.subplot(2, 1, 1)\n", "time_axis = np.linspace(0, len(audio1) / 16000, len(audio1))\n", "plt.plot(time_axis, audio1)\n", "plt.title('Generated Audio Waveform')\n", "plt.xlabel('Time (seconds)')\n", "plt.ylabel('Amplitude')\n", "plt.grid(True)\n", "\n", "# Plot spectrogram\n", "plt.subplot(2, 1, 2)\n", "plt.specgram(audio1, Fs=16000, cmap='viridis')\n", "plt.title('Audio Spectrogram')\n", "plt.xlabel('Time (seconds)')\n", "plt.ylabel('Frequency (Hz)')\n", "plt.colorbar(label='Power (dB)')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Audio statistics:\")\n", "print(f\"- Sample rate: 16000 Hz\")\n", "print(f\"- Duration: {len(audio1) / 16000:.2f} seconds\")\n", "print(f\"- Min amplitude: {audio1.min():.4f}\")\n", "print(f\"- Max amplitude: {audio1.max():.4f}\")\n", "print(f\"- Mean amplitude: {audio1.mean():.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Experiment with Different Speaker Embeddings\n", "\n", "Let's try different speaker voices by using different embeddings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_with_different_speakers(text, num_speakers=3):\n", "    \"\"\"\n", "    Generate speech with different speaker voices.\n", "    \n", "    Args:\n", "        text (str): Text to convert to speech\n", "        num_speakers (int): Number of different speakers to try\n", "    \"\"\"\n", "    print(f\"Generating speech with {num_speakers} different speakers...\")\n", "    \n", "    # Try different speaker embeddings\n", "    speaker_indices = [7306, 4077, 5799]  # Different speakers from the dataset\n", "    \n", "    for i, speaker_idx in enumerate(speaker_indices[:num_speakers]):\n", "        print(f\"\\nSpeaker {i+1} (index {speaker_idx}):\")\n", "        \n", "        # Load speaker embedding\n", "        speaker_emb = torch.tensor(embeddings_dataset[speaker_idx][\"xvector\"]).unsqueeze(0)\n", "        \n", "        # Process text\n", "        inputs = processor(text=text, return_tensors=\"pt\")\n", "        \n", "        # Generate speech\n", "        with torch.no_grad():\n", "            speech = model.generate_speech(inputs[\"input_ids\"], speaker_emb, vocoder=vocoder)\n", "        \n", "        audio = speech.numpy()\n", "        \n", "        # Save and display\n", "        filename = f\"speaker_{i+1}_output.wav\"\n", "        sf.write(filename, audio, samplerate=16000)\n", "        print(f\"Audio saved as: {filename}\")\n", "        \n", "        # Display audio player\n", "        display(Audio(audio, rate=16000))\n", "\n", "# Test with different speakers\n", "test_text = \"This is a test of different speaker voices using the same text-to-speech model.\"\n", "generate_with_different_speakers(test_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 9: Model Information and Technical Details\n", "\n", "Let's examine the technical details of our TTS model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display model information\n", "print(\"=== SpeechT5 TTS Model Information ===\")\n", "print(f\"Model name: microsoft/speecht5_tts\")\n", "print(f\"Model class: {type(model).__name__}\")\n", "print(f\"Processor class: {type(processor).__name__}\")\n", "print(f\"Vocoder class: {type(vocoder).__name__}\")\n", "\n", "print(\"\\n=== Model Configuration ===\")\n", "print(f\"Vocabulary size: {model.config.vocab_size}\")\n", "print(f\"Hidden size: {model.config.hidden_size}\")\n", "print(f\"Number of attention heads: {model.config.encoder_attention_heads}\")\n", "print(f\"Number of encoder layers: {model.config.encoder_layers}\")\n", "print(f\"Number of decoder layers: {model.config.decoder_layers}\")\n", "\n", "print(\"\\n=== Audio Configuration ===\")\n", "print(f\"Sampling rate: 16000 Hz\")\n", "print(f\"Audio format: WAV\")\n", "print(f\"Bit depth: 16-bit\")\n", "\n", "# Count model parameters\n", "total_params = sum(p.numel() for p in model.parameters())\n", "trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "\n", "print(f\"\\n=== Model Size ===\")\n", "print(f\"Total parameters: {total_params:,}\")\n", "print(f\"Trainable parameters: {trainable_params:,}\")\n", "print(f\"Model size: ~{total_params * 4 / (1024**2):.1f} MB (float32)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 10: Challenges and Observations\n", "\n", "### Challenges Faced:\n", "1. **Model Loading**: Large model files require significant memory and download time\n", "2. **Speaker Embeddings**: SpeechT5 requires speaker embeddings, which adds complexity\n", "3. **Audio Quality**: Balancing model size with audio quality\n", "4. **Processing Time**: Inference can be slow for longer texts\n", "\n", "### Key Observations:\n", "1. **Model Architecture**: SpeechT5 uses a unified encoder-decoder architecture\n", "2. **Voice Variety**: Different speaker embeddings produce noticeably different voices\n", "3. **Audio Quality**: Generated speech is natural-sounding with good prosody\n", "4. **Flexibility**: Model handles various text lengths and complexity levels well\n", "\n", "### Potential Improvements:\n", "1. **Custom Voice Training**: Fine-tune with specific speaker data\n", "2. **Real-time Processing**: Optimize for streaming applications\n", "3. **Multi-language Support**: Extend to other languages\n", "4. **Emotion Control**: Add emotional expression capabilities"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "This notebook demonstrates a complete implementation of text-to-speech using Hugging Face's SpeechT5 model. We successfully:\n", "\n", "1. ✅ Loaded a pre-trained TTS model from Hugging Face\n", "2. ✅ Performed inference to convert text to speech\n", "3. ✅ Generated audio files with different speaker voices\n", "4. ✅ Analyzed the model's technical specifications\n", "5. ✅ Visualized audio waveforms and spectrograms\n", "\n", "The SpeechT5 model provides high-quality text-to-speech synthesis with the flexibility to use different speaker voices through embeddings. This technology has applications in:\n", "\n", "- **Accessibility**: Screen readers and assistive technologies\n", "- **Content Creation**: Automated narration and voiceovers\n", "- **Virtual Assistants**: Natural voice interfaces\n", "- **Education**: Language learning and pronunciation tools\n", "- **Entertainment**: Character voices in games and media\n", "\n", "### Next Steps:\n", "- Experiment with different TTS models (FastSpeech2, Tacotron2, etc.)\n", "- Explore real-time TTS applications\n", "- Investigate voice cloning and custom speaker training\n", "- Deploy TTS models using Hugging Face Spaces or other platforms"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}