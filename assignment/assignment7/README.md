# Assignment 7: Text-to-Speech (TTS) with Hugging Face

## Overview

This assignment demonstrates how to clone and use a pre-trained Text-to-Speech (TTS) model from Hugging Face to convert text into natural-sounding speech. We use Microsoft's SpeechT5 model, which provides high-quality speech synthesis with support for different speaker voices.

## Objectives

- ✅ Learn how to clone a pre-trained TTS model from Hugging Face
- ✅ Perform inference to convert input text into spoken audio
- ✅ Understand the basics of TTS technology and model architecture
- ✅ Experiment with different speaker voices
- ✅ Analyze audio quality and model performance

## Files Included

1. **`tts_inference.ipynb`** - Complete Jupyter notebook with step-by-step implementation
2. **`tts_inference.py`** - Python script version for command-line execution
3. **`requirements.txt`** - List of required Python packages
4. **`README.md`** - This documentation file

## Model Information

- **Model**: Microsoft SpeechT5 TTS (`microsoft/speecht5_tts`)
- **Vocoder**: SpeechT5 HiFi-GAN (`microsoft/speecht5_hifigan`)
- **Speaker Embeddings**: CMU ARCTIC X-vectors (`Matthijs/cmu-arctic-xvectors`)
- **Output Format**: 16kHz WAV audio files
- **Architecture**: Unified encoder-decoder transformer

## Installation

### Prerequisites

- Python 3.7 or higher
- pip package manager
- At least 4GB of available RAM
- Internet connection for model downloading

### Setup Instructions

1. **Clone or download the assignment files**
   ```bash
   # Navigate to the assignment directory
   cd assignment/assignment7
   ```

2. **Install required packages**
   ```bash
   pip install -r requirements.txt
   ```

   Or install packages individually:
   ```bash
   pip install torch torchaudio transformers datasets soundfile matplotlib numpy jupyter
   ```

3. **Verify installation**
   ```python
   import torch
   import transformers
   print(f"PyTorch: {torch.__version__}")
   print(f"Transformers: {transformers.__version__}")
   ```

## Usage

### Option 1: Jupyter Notebook (Recommended)

1. **Start Jupyter Notebook**
   ```bash
   jupyter notebook
   ```

2. **Open `tts_inference.ipynb`**

3. **Run all cells sequentially** - The notebook includes:
   - Model loading and setup
   - Text-to-speech inference examples
   - Audio visualization and analysis
   - Multiple speaker voice demonstrations
   - Technical model information

### Option 2: Python Script

1. **Run the complete demo**
   ```bash
   python tts_inference.py
   ```

2. **The script will**:
   - Load the SpeechT5 model automatically
   - Generate speech for several test sentences
   - Create audio files in the `tts_outputs/` directory
   - Display model information and statistics
   - Demonstrate different speaker voices

## Key Features Demonstrated

### 1. Model Loading
```python
# Load processor and model
processor = SpeechT5Processor.from_pretrained("microsoft/speecht5_tts")
model = SpeechT5ForTextToSpeech.from_pretrained("microsoft/speecht5_tts")
vocoder = SpeechT5HifiGan.from_pretrained("microsoft/speecht5_hifigan")
```

### 2. Text-to-Speech Inference
```python
# Process text and generate speech
inputs = processor(text=text, return_tensors="pt")
with torch.no_grad():
    speech = model.generate_speech(inputs["input_ids"], speaker_embeddings, vocoder=vocoder)
audio_output = speech.numpy()
```

### 3. Speaker Voice Variation
```python
# Use different speaker embeddings for voice variety
speaker_embeddings = torch.tensor(embeddings_dataset[speaker_index]["xvector"]).unsqueeze(0)
```

### 4. Audio Analysis
- Waveform visualization
- Spectrogram analysis
- Audio statistics and quality metrics

## Expected Outputs

After running the code, you should have:

1. **Audio Files** (`.wav` format):
   - `test_1_output.wav` - Basic demonstration
   - `test_2_output.wav` - Complex sentence
   - `test_3_output.wav` - Custom text
   - `speaker_1_output.wav` - Different speaker voices
   - `speaker_2_output.wav`
   - `speaker_3_output.wav`

2. **Visualizations**:
   - Audio waveform plots
   - Spectrograms showing frequency content
   - Amplitude distribution histograms

3. **Model Information**:
   - Technical specifications
   - Parameter counts
   - Performance metrics

## Technical Details

### Model Architecture
- **SpeechT5**: Unified encoder-decoder transformer for TTS
- **Parameters**: ~200M parameters
- **Input**: Text tokens
- **Output**: Mel-spectrogram features
- **Vocoder**: HiFi-GAN for waveform generation

### Audio Specifications
- **Sample Rate**: 16,000 Hz
- **Format**: 16-bit WAV
- **Channels**: Mono
- **Quality**: High-fidelity speech synthesis

### Performance Considerations
- **Memory Usage**: ~2-3GB RAM during inference
- **Processing Time**: ~1-2 seconds per sentence
- **Model Size**: ~800MB total download

## Challenges and Solutions

### 1. Model Loading
- **Challenge**: Large model files and memory requirements
- **Solution**: Efficient loading with automatic caching

### 2. Speaker Embeddings
- **Challenge**: SpeechT5 requires speaker embeddings
- **Solution**: Use pre-computed X-vector embeddings from CMU ARCTIC

### 3. Audio Quality
- **Challenge**: Balancing quality with processing speed
- **Solution**: Use HiFi-GAN vocoder for high-quality output

### 4. Voice Variety
- **Challenge**: Limited to available speaker embeddings
- **Solution**: Demonstrate multiple speakers from the dataset

## Applications

This TTS technology can be used for:

- **Accessibility**: Screen readers and assistive technologies
- **Content Creation**: Automated narration and voiceovers
- **Virtual Assistants**: Natural voice interfaces
- **Education**: Language learning and pronunciation tools
- **Entertainment**: Character voices in games and media

## Troubleshooting

### Common Issues

1. **Out of Memory Error**
   - Reduce batch size or use CPU instead of GPU
   - Close other applications to free RAM

2. **Model Download Fails**
   - Check internet connection
   - Try downloading models manually

3. **Audio Playback Issues**
   - Ensure audio drivers are installed
   - Use external audio player for WAV files

4. **Import Errors**
   - Verify all packages are installed correctly
   - Check Python version compatibility

### Getting Help

If you encounter issues:
1. Check the error messages carefully
2. Verify all dependencies are installed
3. Ensure sufficient system resources
4. Try running individual code sections

## Next Steps

After completing this assignment, consider exploring:

1. **Different TTS Models**: FastSpeech2, Tacotron2, WaveNet
2. **Voice Cloning**: Custom speaker training
3. **Real-time TTS**: Streaming applications
4. **Multi-language Support**: International TTS models
5. **Deployment**: Hugging Face Spaces or cloud platforms

## References

- [SpeechT5 Paper](https://arxiv.org/abs/2110.07205)
- [Hugging Face Transformers Documentation](https://huggingface.co/docs/transformers/)
- [SpeechT5 Model Card](https://huggingface.co/microsoft/speecht5_tts)
- [CMU ARCTIC Database](http://festvox.org/cmu_arctic/)

## Assignment Completion Checklist

- [ ] Successfully loaded SpeechT5 TTS model
- [ ] Generated speech from text input
- [ ] Saved audio files in WAV format
- [ ] Demonstrated multiple speaker voices
- [ ] Analyzed audio quality and characteristics
- [ ] Documented challenges and solutions
- [ ] Included code comments and explanations

---

**Assignment 7 Complete** ✅

This implementation provides a comprehensive introduction to text-to-speech technology using state-of-the-art models from Hugging Face, demonstrating both the technical aspects and practical applications of TTS systems.
