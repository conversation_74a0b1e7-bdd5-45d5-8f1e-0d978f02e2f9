# Text-to-Speech Assignment Requirements
# Install with: pip install -r requirements.txt

# Core ML libraries
torch>=1.9.0
torchaudio>=0.9.0

# Hugging Face libraries
transformers>=4.21.0
datasets>=2.0.0

# Audio processing
soundfile>=0.10.0
librosa>=0.9.0

# Data science and visualization
numpy>=1.21.0
matplotlib>=3.5.0
scipy>=1.7.0

# Jupyter notebook support (optional)
jupyter>=1.0.0
ipython>=7.0.0
ipywidgets>=7.6.0

# Additional utilities
tqdm>=4.62.0
requests>=2.25.0
