#!/usr/bin/env python3
"""
Quick TTS Demo - Assignment 7

A simplified version of the TTS implementation for quick testing.
This script provides a minimal example of text-to-speech conversion.

Usage:
    python quick_demo.py
"""

import torch
import soundfile as sf
from transformers import SpeechT5Processor, SpeechT5ForTextToSpeech, SpeechT5HifiGan
from datasets import load_dataset
import warnings
import os

# Suppress warnings
warnings.filterwarnings('ignore')

def quick_tts_demo():
    """Quick demonstration of TTS functionality."""
    
    print("🎤 Quick TTS Demo - Assignment 7")
    print("=" * 40)
    
    try:
        # Load models
        print("📥 Loading models (this may take a few minutes on first run)...")
        
        processor = SpeechT5Processor.from_pretrained("microsoft/speecht5_tts")
        model = SpeechT5ForTextToSpeech.from_pretrained("microsoft/speecht5_tts")
        vocoder = SpeechT5HifiGan.from_pretrained("microsoft/speecht5_hifigan")
        
        print("✅ Models loaded successfully!")
        
        # Load speaker embeddings
        print("📥 Loading speaker embeddings...")
        embeddings_dataset = load_dataset("Matthijs/cmu-arctic-xvectors", split="validation")
        speaker_embeddings = torch.tensor(embeddings_dataset[7306]["xvector"]).unsqueeze(0)
        
        print("✅ Speaker embeddings loaded!")
        
        # Test text
        test_text = "Hello! This is a quick demonstration of text to speech using Hugging Face transformers. The model is working correctly!"
        
        print(f"🗣️  Converting text to speech...")
        print(f"📝 Text: '{test_text}'")
        
        # Generate speech
        inputs = processor(text=test_text, return_tensors="pt")
        
        with torch.no_grad():
            speech = model.generate_speech(inputs["input_ids"], speaker_embeddings, vocoder=vocoder)
        
        # Convert to numpy and save
        audio = speech.numpy()
        output_file = "quick_demo_output.wav"
        sf.write(output_file, audio, samplerate=16000)
        
        print("✅ Speech generated successfully!")
        print(f"💾 Audio saved as: {output_file}")
        print(f"⏱️  Duration: {len(audio) / 16000:.2f} seconds")
        print(f"📊 Audio shape: {audio.shape}")
        
        # Display system info
        print("\n🔧 System Information:")
        print(f"   PyTorch version: {torch.__version__}")
        print(f"   CUDA available: {torch.cuda.is_available()}")
        print(f"   Audio sample rate: 16000 Hz")
        print(f"   Audio format: WAV")
        
        print("\n🎉 Demo completed successfully!")
        print(f"   You can play '{output_file}' with any audio player.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error occurred: {str(e)}")
        print("\n🔧 Troubleshooting tips:")
        print("   1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("   2. Check your internet connection for model downloads")
        print("   3. Ensure you have sufficient RAM (4GB+ recommended)")
        return False

def check_dependencies():
    """Check if required packages are installed."""
    
    print("🔍 Checking dependencies...")
    
    required_packages = [
        ('torch', 'PyTorch'),
        ('transformers', 'Hugging Face Transformers'),
        ('datasets', 'Hugging Face Datasets'),
        ('soundfile', 'SoundFile')
    ]
    
    missing_packages = []
    
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} - NOT INSTALLED")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("   Install with: pip install " + " ".join(missing_packages))
        return False
    else:
        print("✅ All dependencies are installed!")
        return True

if __name__ == "__main__":
    print("🚀 Starting Quick TTS Demo...")
    
    # Check dependencies first
    if check_dependencies():
        print()
        success = quick_tts_demo()
        
        if success:
            print("\n🎯 Next steps:")
            print("   1. Open 'tts_inference.ipynb' for the complete tutorial")
            print("   2. Run 'python tts_inference.py' for the full demo")
            print("   3. Experiment with different text inputs")
        else:
            print("\n🔧 Please fix the errors above and try again.")
    else:
        print("\n🔧 Please install missing dependencies and try again.")
        print("   Run: pip install -r requirements.txt")
