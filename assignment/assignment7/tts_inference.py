#!/usr/bin/env python3
"""
Text-to-Speech (TTS) Model Inference with Hugging Face

Assignment 7: Clone and Perform Inference with Hugging Face TTS Model

This script demonstrates how to:
1. Load a pre-trained TTS model from Hugging Face
2. Perform inference to convert text to speech
3. Save generated audio files
4. Experiment with different speaker voices

Author: Assignment 7 Solution
Model Used: Microsoft SpeechT5 TTS
"""

import torch
import soundfile as sf
import numpy as np
from transformers import SpeechT5Processor, SpeechT5ForTextToSpeech, SpeechT5HifiGan
from datasets import load_dataset
import warnings
import os
import matplotlib.pyplot as plt

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

class TTSInference:
    """Text-to-Speech inference class using SpeechT5 model."""
    
    def __init__(self):
        """Initialize the TTS model, processor, and vocoder."""
        print("Initializing TTS system...")
        
        # Load the processor and models
        print("Loading SpeechT5 processor...")
        self.processor = SpeechT5Processor.from_pretrained("microsoft/speecht5_tts")
        
        print("Loading SpeechT5 TTS model...")
        self.model = SpeechT5ForTextToSpeech.from_pretrained("microsoft/speecht5_tts")
        
        print("Loading SpeechT5 vocoder (HiFi-GAN)...")
        self.vocoder = SpeechT5HifiGan.from_pretrained("microsoft/speecht5_hifigan")
        
        # Load speaker embeddings
        print("Loading speaker embeddings...")
        self.embeddings_dataset = load_dataset("Matthijs/cmu-arctic-xvectors", split="validation")
        
        # Default speaker embedding
        self.default_speaker_embedding = torch.tensor(
            self.embeddings_dataset[7306]["xvector"]
        ).unsqueeze(0)
        
        print("TTS system initialized successfully!")
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
    
    def text_to_speech(self, text, speaker_embedding=None, save_path=None):
        """
        Convert text to speech using SpeechT5 model.
        
        Args:
            text (str): Input text to convert to speech
            speaker_embedding (torch.Tensor, optional): Speaker embedding to use
            save_path (str, optional): Path to save the audio file
        
        Returns:
            numpy.ndarray: Generated audio waveform
        """
        print(f"Converting text to speech: '{text[:50]}{'...' if len(text) > 50 else ''}'")
        
        # Use default speaker embedding if none provided
        if speaker_embedding is None:
            speaker_embedding = self.default_speaker_embedding
        
        # Process the input text
        inputs = self.processor(text=text, return_tensors="pt")
        
        # Generate speech with no gradient computation (inference mode)
        with torch.no_grad():
            speech = self.model.generate_speech(
                inputs["input_ids"], 
                speaker_embedding, 
                vocoder=self.vocoder
            )
        
        # Convert to numpy array
        audio_output = speech.numpy()
        
        # Save audio file if path is provided
        if save_path:
            sf.write(save_path, audio_output, samplerate=16000)
            print(f"Audio saved to: {save_path}")
        
        return audio_output
    
    def get_speaker_embedding(self, speaker_index):
        """
        Get speaker embedding by index.
        
        Args:
            speaker_index (int): Index of speaker in the dataset
        
        Returns:
            torch.Tensor: Speaker embedding
        """
        return torch.tensor(
            self.embeddings_dataset[speaker_index]["xvector"]
        ).unsqueeze(0)
    
    def generate_with_multiple_speakers(self, text, speaker_indices=None, output_dir="outputs"):
        """
        Generate speech with multiple different speakers.
        
        Args:
            text (str): Text to convert to speech
            speaker_indices (list, optional): List of speaker indices to use
            output_dir (str): Directory to save output files
        
        Returns:
            list: List of generated audio arrays
        """
        if speaker_indices is None:
            speaker_indices = [7306, 4077, 5799]  # Default speakers
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        audio_outputs = []
        
        print(f"Generating speech with {len(speaker_indices)} different speakers...")
        
        for i, speaker_idx in enumerate(speaker_indices):
            print(f"\nGenerating with Speaker {i+1} (index {speaker_idx})...")
            
            # Get speaker embedding
            speaker_emb = self.get_speaker_embedding(speaker_idx)
            
            # Generate speech
            audio = self.text_to_speech(
                text, 
                speaker_embedding=speaker_emb,
                save_path=os.path.join(output_dir, f"speaker_{i+1}_output.wav")
            )
            
            audio_outputs.append(audio)
            print(f"Audio duration: {len(audio) / 16000:.2f} seconds")
        
        return audio_outputs
    
    def analyze_audio(self, audio, title="Audio Analysis", save_plot=None):
        """
        Analyze and visualize audio characteristics.
        
        Args:
            audio (numpy.ndarray): Audio waveform
            title (str): Title for the plot
            save_plot (str, optional): Path to save the plot
        """
        plt.figure(figsize=(12, 8))
        
        # Plot waveform
        plt.subplot(3, 1, 1)
        time_axis = np.linspace(0, len(audio) / 16000, len(audio))
        plt.plot(time_axis, audio)
        plt.title(f'{title} - Waveform')
        plt.xlabel('Time (seconds)')
        plt.ylabel('Amplitude')
        plt.grid(True)
        
        # Plot spectrogram
        plt.subplot(3, 1, 2)
        plt.specgram(audio, Fs=16000, cmap='viridis')
        plt.title(f'{title} - Spectrogram')
        plt.xlabel('Time (seconds)')
        plt.ylabel('Frequency (Hz)')
        plt.colorbar(label='Power (dB)')
        
        # Plot amplitude histogram
        plt.subplot(3, 1, 3)
        plt.hist(audio, bins=50, alpha=0.7, edgecolor='black')
        plt.title(f'{title} - Amplitude Distribution')
        plt.xlabel('Amplitude')
        plt.ylabel('Frequency')
        plt.grid(True)
        
        plt.tight_layout()
        
        if save_plot:
            plt.savefig(save_plot, dpi=300, bbox_inches='tight')
            print(f"Plot saved to: {save_plot}")
        
        plt.show()
        
        # Print statistics
        print(f"\n{title} Statistics:")
        print(f"- Sample rate: 16000 Hz")
        print(f"- Duration: {len(audio) / 16000:.2f} seconds")
        print(f"- Min amplitude: {audio.min():.4f}")
        print(f"- Max amplitude: {audio.max():.4f}")
        print(f"- Mean amplitude: {audio.mean():.4f}")
        print(f"- RMS amplitude: {np.sqrt(np.mean(audio**2)):.4f}")
    
    def get_model_info(self):
        """Display detailed information about the loaded models."""
        print("=== SpeechT5 TTS Model Information ===")
        print(f"Model name: microsoft/speecht5_tts")
        print(f"Model class: {type(self.model).__name__}")
        print(f"Processor class: {type(self.processor).__name__}")
        print(f"Vocoder class: {type(self.vocoder).__name__}")
        
        print("\n=== Model Configuration ===")
        config = self.model.config
        print(f"Vocabulary size: {config.vocab_size}")
        print(f"Hidden size: {config.hidden_size}")
        print(f"Number of attention heads: {config.encoder_attention_heads}")
        print(f"Number of encoder layers: {config.encoder_layers}")
        print(f"Number of decoder layers: {config.decoder_layers}")
        
        print("\n=== Audio Configuration ===")
        print(f"Sampling rate: 16000 Hz")
        print(f"Audio format: WAV")
        print(f"Bit depth: 16-bit")
        
        # Count model parameters
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"\n=== Model Size ===")
        print(f"Total parameters: {total_params:,}")
        print(f"Trainable parameters: {trainable_params:,}")
        print(f"Model size: ~{total_params * 4 / (1024**2):.1f} MB (float32)")


def main():
    """Main function to demonstrate TTS functionality."""
    print("=== Text-to-Speech Demo with Hugging Face SpeechT5 ===\n")
    
    # Initialize TTS system
    tts = TTSInference()
    
    # Display model information
    print("\n" + "="*60)
    tts.get_model_info()
    print("="*60)
    
    # Test sentences
    test_sentences = [
        "Hello, this is a demonstration of text to speech using Hugging Face transformers.",
        "Artificial intelligence and machine learning are revolutionizing the way we interact with technology.",
        "Welcome to the world of natural language processing and speech synthesis!",
        "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet."
    ]
    
    # Create output directory
    output_dir = "tts_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"\n=== Generating Speech for {len(test_sentences)} Test Sentences ===")
    
    # Generate speech for each test sentence
    for i, text in enumerate(test_sentences, 1):
        print(f"\n--- Test {i} ---")
        audio = tts.text_to_speech(
            text, 
            save_path=os.path.join(output_dir, f"test_{i}_output.wav")
        )
        
        # Analyze the first audio sample
        if i == 1:
            tts.analyze_audio(
                audio, 
                title=f"Test {i} Audio Analysis",
                save_plot=os.path.join(output_dir, f"test_{i}_analysis.png")
            )
    
    # Demonstrate multiple speakers
    print(f"\n=== Demonstrating Multiple Speaker Voices ===")
    multi_speaker_text = "This is a test of different speaker voices using the same text-to-speech model."
    
    speaker_audios = tts.generate_with_multiple_speakers(
        multi_speaker_text,
        speaker_indices=[7306, 4077, 5799],
        output_dir=output_dir
    )
    
    print(f"\n=== Demo Complete ===")
    print(f"All audio files saved to: {output_dir}/")
    print("You can play the generated WAV files using any audio player.")
    
    # Summary
    print(f"\n=== Summary ===")
    print("✅ Successfully loaded SpeechT5 TTS model from Hugging Face")
    print("✅ Generated speech from multiple text inputs")
    print("✅ Demonstrated different speaker voices")
    print("✅ Analyzed audio characteristics")
    print("✅ Saved all outputs for review")
    
    print(f"\nChallenges addressed:")
    print("- Model loading and memory management")
    print("- Speaker embedding integration")
    print("- Audio quality optimization")
    print("- Batch processing for multiple inputs")


if __name__ == "__main__":
    main()
