<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Entertainment Recommendation Bot - Workshop 03</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .slide.active {
            display: flex;
        }

        .slide h1 {
            font-size: 4rem;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .slide h2 {
            font-size: 3.2rem;
            margin-bottom: 25px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .slide h3 {
            font-size: 2.4rem;
            margin-bottom: 20px;
            color: #FFD700;
        }

        .slide p {
            font-size: 1.6rem;
            line-height: 1.6;
            margin-bottom: 20px;
            max-width: 80%;
        }

        .slide ul {
            text-align: left;
            font-size: 1.5rem;
            line-height: 1.8;
            max-width: 80%;
        }

        .slide li {
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
        }

        .slide li::before {
            content: "🎬";
            position: absolute;
            left: 0;
        }

        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            width: 100%;
            max-width: 1200px;
            margin-top: 30px;
        }

        .tech-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(5px);
            transition: transform 0.3s ease;
        }

        .tech-item:hover {
            transform: translateY(-10px);
        }

        .tech-item h4 {
            font-size: 1.6rem;
            margin-bottom: 15px;
            color: #FFD700;
        }

        .tech-item p {
            font-size: 1.3rem;
        }

        .architecture-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: 1000px;
            margin-top: 30px;
        }

        .flow-step {
            background: rgba(255, 255, 255, 0.2);
            padding: 25px;
            border-radius: 10px;
            border: 2px solid #FFD700;
            flex: 1;
            margin: 0 10px;
            position: relative;
        }

        .flow-step h4 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #FFD700;
        }

        .flow-step p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .flow-step::after {
            content: "→";
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2rem;
            color: #FFD700;
        }

        .flow-step:last-child::after {
            display: none;
        }

        .demo-code {
            background: rgba(0, 0, 0, 0.4);
            padding: 30px;
            border-radius: 10px;
            border-left: 5px solid #FFD700;
            font-family: 'Courier New', monospace;
            text-align: left;
            max-width: 90%;
            margin: 20px 0;
            overflow-x: auto;
            font-size: 1.3rem;
            line-height: 1.6;
        }

        .code-sections {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            width: 100%;
            max-width: 1200px;
        }

        .code-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .code-section h4 {
            color: #FFD700;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        /* Syntax highlighting */
        .keyword {
            color: #569cd6;
            font-weight: bold;
        }

        .function {
            color: #dcdcaa;
        }

        .class-name {
            color: #4ec9b0;
        }

        .string {
            color: #ce9178;
        }

        .comment {
            color: #6a9955;
            font-style: italic;
        }

        .number {
            color: #b5cea8;
        }

        .operator {
            color: #d4d4d4;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.3);
            border: 2px solid #FFD700;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .nav-btn:hover {
            background: #FFD700;
            color: #333;
            transform: translateY(-2px);
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1.1rem;
            z-index: 1000;
        }

        .keyboard-help {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            font-size: 0.9rem;
            z-index: 1000;
        }

        .emoji {
            font-size: 4.5rem;
            margin-bottom: 25px;
        }

        .highlight {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            width: 100%;
            max-width: 1200px;
            margin-top: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.2);
        }

        .results-showcase {
            display: flex;
            justify-content: space-around;
            width: 100%;
            max-width: 1000px;
            margin-top: 30px;
        }

        .result-item {
            text-align: center;
            flex: 1;
            margin: 0 20px;
        }

        .result-number {
            font-size: 3.5rem;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 15px;
        }

        .result-item p {
            font-size: 1.5rem;
        }

        @media (max-width: 768px) {
            .slide {
                padding: 30px 20px;
            }
            
            .slide h1 {
                font-size: 3.5rem;
            }
            
            .slide h2 {
                font-size: 2.8rem;
            }

            .slide h3 {
                font-size: 2.2rem;
            }
            
            .slide p {
                font-size: 1.6rem;
            }

            .slide ul {
                font-size: 1.4rem;
            }
            
            .tech-stack,
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .architecture-flow {
                flex-direction: column;
                gap: 20px;
            }
            
            .flow-step::after {
                content: "↓";
                right: 50%;
                bottom: -30px;
                top: auto;
                transform: translateX(50%);
            }
            
            .code-sections {
                grid-template-columns: 1fr;
            }
            
            .demo-code {
                font-size: 1rem;
                padding: 20px;
            }

            .emoji {
                font-size: 4rem;
            }

            .tech-item h4 {
                font-size: 1.6rem;
            }

            .tech-item p {
                font-size: 1.2rem;
            }

            .flow-step h4 {
                font-size: 1.4rem;
            }

            .flow-step p {
                font-size: 1.1rem;
            }

            .code-section h4 {
                font-size: 1.4rem;
            }

            .result-number {
                font-size: 3rem;
            }

            .result-item p {
                font-size: 1.4rem;
            }
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">13</span>
        </div>

        <!-- Slide 1: Title -->
        <div class="slide active fade-in">
            <div class="emoji">🎬🤖</div>
            <h1>Entertainment Recommendation Bot</h1>
            <h3>Workshop 03 - AI-Powered Content Discovery</h3>
            <p>Hệ thống gợi ý phim và chương trình TV thông minh sử dụng AI và Machine Learning</p>
            <p><strong>Công nghệ:</strong> Azure OpenAI • ChromaDB • Vector Embeddings • Facebook MMS TTS</p>
        </div>

        <!-- Slide 2: Problem Statement -->
        <div class="slide">
            <div class="emoji">🎯</div>
            <h2>Vấn đề cần giải quyết</h2>
            <ul>
                <li><strong>Information Overload:</strong> Hàng nghìn phim/TV shows khó lựa chọn</li>
                <li><strong>Cold Start Problem:</strong> Khó gợi ý cho user mới không có lịch sử xem</li>
                <li><strong>Understanding Preferences:</strong> Hiểu ý nghĩa thực sự của user preferences</li>
                <li><strong>Personalization:</strong> Gợi ý phù hợp với từng cá nhân</li>
                <li><strong>Explainability:</strong> Giải thích tại sao recommend nội dung này</li>
            </ul>
        </div>

        <!-- Slide 3: Solution Overview -->
        <div class="slide">
            <div class="emoji">💡</div>
            <h2>Giải pháp AI-powered Recommendation</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h4>🔍 Smart Search</h4>
                    <p>Sử dụng AI để hiểu ý định của user và tìm nội dung tương tự</p>
                </div>
                <div class="feature-card">
                    <h4>🧠 Data Storage</h4>
                    <p>ChromaDB lưu trữ thông tin và mối quan hệ giữa các nội dung</p>
                </div>
                <div class="feature-card">
                    <h4>🎯 Intelligent Recommendations</h4>
                    <p>GPT-4o-mini tạo ra gợi ý cá nhân hóa với lý do rõ ràng</p>
                </div>
                <div class="feature-card">
                    <h4>🔊 Voice Output</h4>
                    <p>Chuyển đổi text thành giọng nói tự nhiên</p>
                </div>
            </div>
        </div>

        <!-- Slide 4: Tech Stack -->
        <div class="slide">
            <div class="emoji">🛠️</div>
            <h2>Technology Stack</h2>
            <div class="tech-stack">
                <div class="tech-item">
                    <h4>🤖 Azure OpenAI</h4>
                    <p><strong>Embeddings:</strong> text-embedding-3-small</p>
                    <p><strong>LLM:</strong> GPT-4o-mini</p>
                    <p>Semantic understanding & generation</p>
                </div>
                <div class="tech-item">
                    <h4>🗄️ ChromaDB</h4>
                    <p>Vector database for similarity search</p>
                    <p>Persistent storage & fast retrieval</p>
                </div>
                <div class="tech-item">
                    <h4>🎤 Facebook MMS</h4>
                    <p>MMS TTS for Text-to-Speech</p>
                    <p>Natural English audio output</p>
                </div>
                <div class="tech-item">
                    <h4>🐍 Python</h4>
                    <p>Core application framework</p>
                    <p>CLI interface & orchestration</p>
                </div>
            </div>
        </div>

        <!-- Slide 5: Architecture -->
        <div class="slide">
            <div class="emoji">🏗️</div>
            <h2>AI System Architecture Flow</h2>
            <div class="architecture-flow">
                <div class="flow-step">
                    <h4>1. Input Layer</h4>
                    <p>CLI interface receives user query</p>
                    <p><em>"Sci-fi movies like Inception"</em></p>
                </div>
                <div class="flow-step">
                    <h4>2. Smart Search</h4>
                    <p>Semantic search with embeddings</p>
                    <p>Top-3 matches from ChromaDB</p>
                </div>
                <div class="flow-step">
                    <h4>3. AI Recommendation</h4>
                    <p>GPT-4o-mini generates natural responses</p>
                    <p>Conversational recommendations</p>
                </div>
                <div class="flow-step">
                    <h4>4. Output</h4>
                    <p>Text + Audio in results folder</p>
                    <p>Facebook MMS TTS</p>
                </div>
            </div>
        </div>

        <!-- Slide 6: Key Features -->
        <div class="slide">
            <div class="emoji">⭐</div>
            <h2>Key Features Implemented</h2>
            <ul>
                <li><strong>Content-Based Filtering:</strong> Phân tích nội dung để tìm sự tương đồng</li>
                <li><strong>No History Required:</strong> Không cần lịch sử xem, chỉ cần miêu tả sở thích</li>
                <li><strong>Explainable AI:</strong> Mỗi recommendation có lý do chi tiết</li>
                <li><strong>Multi-Modal Output:</strong> Cả text và audio response</li>
                <li><strong>Rich Database:</strong> Thông tin đầy đủ về thể loại, rating, năm</li>
                <li><strong>Interactive Interface:</strong> CLI thân thiện với examples và help</li>
                <li><strong>Persistent Data:</strong> Dữ liệu được lưu trữ qua các phiên làm việc</li>
            </ul>
        </div>

        <!-- Slide 13: Conclusion & Future -->
        <div class="slide">
            <div class="emoji">🚀</div>
            <h2>Kết luận & Hướng phát triển</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; width: 100%; max-width: 1000px;">
                <div>
                    <h3>✅ Đã đạt được</h3>
                    <ul style="text-align: left;">
                        <li>AI architecture hoạt động hiệu quả</li>
                        <li>Semantic search accuracy cao</li>
                        <li>Explainable recommendations</li>
                        <li>Multi-modal output (text + audio)</li>
                        <li>User-friendly CLI interface</li>
                    </ul>
                </div>
                <div>
                    <h3>🔮 Future Enhancements</h3>
                    <ul style="text-align: left;">
                        <li>Web interface với React/Vue</li>
                        <li>Real-time TMDB API integration</li>
                        <li>User preference learning</li>
                        <li>Collaborative filtering hybrid</li>
                        <li>Multi-language support</li>
                    </ul>
                </div>
            </div>
            <h3 style="margin-top: 40px; color: #FFD700;">Thank you! 🎬✨</h3>
        </div>

        <!-- Keyboard help -->
        <div class="keyboard-help">
            <strong>📋 Shortcuts:</strong><br>
            → / Space: Next<br>
            ←: Previous<br>
            F11: Fullscreen<br>
            Home: First<br>
            End: Last
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            slides[currentSlide].classList.add('fade-in');
            
            // Remove fade-in class after animation
            setTimeout(() => {
                slides[currentSlide].classList.remove('fade-in');
            }, 800);
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        function firstSlide() {
            showSlide(0);
        }

        function lastSlide() {
            showSlide(totalSlides - 1);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    firstSlide();
                    break;
                case 'End':
                    e.preventDefault();
                    lastSlide();
                    break;
                case 'F11':
                    e.preventDefault();
                    if (!document.fullscreenElement) {
                        document.documentElement.requestFullscreen();
                    } else {
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        // Touch/swipe support for mobile
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        });

        document.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const swipeDistance = touchEndX - touchStartX;
            
            if (Math.abs(swipeDistance) > swipeThreshold) {
                if (swipeDistance > 0) {
                    previousSlide();
                } else {
                    nextSlide();
                }
            }
        }

        // Auto-advance slides (optional, commented out)
        // setInterval(nextSlide, 10000); // Auto-advance every 10 seconds
    </script>
</body>
</html>
