[{"title": "Inception", "type": "Movie", "description": "Sci-fi action thriller about dream invasion and layered reality. Directed by <PERSON>. A skilled thief enters people's dreams to steal secrets but is tasked with planting an idea instead.", "genres": ["Action", "Sci-Fi", "Thriller"], "year": 2010, "rating": 8.8}, {"title": "Stranger Things", "type": "TV Show", "description": "Horror sci-fi series about kids in a small town facing supernatural events from an alternate dimension called the Upside Down. Set in the 1980s with nostalgic references.", "genres": ["Horror", "Sci-Fi", "Drama"], "year": 2016, "rating": 8.7}, {"title": "The Matrix", "type": "Movie", "description": "Action sci-fi with philosophical themes about reality and simulation. <PERSON> discovers the world is a computer simulation and joins a rebellion against machine overlords.", "genres": ["Action", "Sci-Fi"], "year": 1999, "rating": 8.7}, {"title": "Friends", "type": "TV Show", "description": "Comedy sitcom about six friends living in New York City, dealing with relationships, careers, and everyday life with humor and heart.", "genres": ["Comedy", "Romance"], "year": 1994, "rating": 8.9}, {"title": "Breaking Bad", "type": "TV Show", "description": "Crime drama about a high school chemistry teacher who turns to cooking methamphetamine after being diagnosed with cancer to secure his family's future.", "genres": ["Crime", "Drama", "Thriller"], "year": 2008, "rating": 9.5}, {"title": "The Dark Knight", "type": "Movie", "description": "Superhero action film featuring <PERSON> facing his greatest challenge against the Joker. A psychological battle between order and chaos in Gotham City.", "genres": ["Action", "Crime", "Drama"], "year": 2008, "rating": 9.0}, {"title": "The Office", "type": "TV Show", "description": "Mockumentary comedy series about employees at a paper company in Scranton, PA. Features awkward humor and character development through daily office life.", "genres": ["Comedy"], "year": 2005, "rating": 8.9}, {"title": "Interstellar", "type": "Movie", "description": "Space exploration epic about humanity's search for a new home planet. Combines hard science fiction with emotional storytelling about family and sacrifice.", "genres": ["Sci-Fi", "Drama"], "year": 2014, "rating": 8.6}, {"title": "Game of Thrones", "type": "TV Show", "description": "Epic fantasy series set in medieval-like world with political intrigue, warfare, and dragons. Multiple storylines converge in a battle for the Iron Throne.", "genres": ["Fantasy", "Drama", "Action"], "year": 2011, "rating": 8.8}, {"title": "Pulp Fiction", "type": "Movie", "description": "Nonlinear crime film with interconnected stories of Los Angeles criminals. Known for its dialogue, pop culture references, and unique narrative structure.", "genres": ["Crime", "Drama"], "year": 1994, "rating": 8.9}, {"title": "The Shawshank Redemption", "type": "Movie", "description": "Drama about hope and friendship in prison. A banker wrongly convicted of murder befriends fellow inmates and becomes instrumental in money laundering operation.", "genres": ["Drama"], "year": 1994, "rating": 9.3}, {"title": "The Godfather", "type": "Movie", "description": "Crime saga about the powerful Italian-American crime family. The aging patriarch transfers control to his reluctant son, exploring themes of power and family.", "genres": ["Crime", "Drama"], "year": 1972, "rating": 9.2}, {"title": "<PERSON><PERSON><PERSON>'s List", "type": "Movie", "description": "Historical drama about <PERSON><PERSON> saving over 1,000 Jews during the Holocaust. A powerful story of humanity in the darkest of times.", "genres": ["Drama", "History", "Biography"], "year": 1993, "rating": 9.0}, {"title": "The Lord of the Rings: The Return of the King", "type": "Movie", "description": "Epic fantasy conclusion where <PERSON><PERSON><PERSON> claims his throne and <PERSON><PERSON><PERSON> destroys the One Ring. Grand battles and emotional character arcs conclude the trilogy.", "genres": ["Adventure", "Drama", "Fantasy"], "year": 2003, "rating": 9.0}, {"title": "<PERSON>", "type": "Movie", "description": "Life story of a simple man who inadvertently influences major historical events. Heartwarming tale about love, friendship, and the American dream.", "genres": ["Drama", "Romance"], "year": 1994, "rating": 8.8}, {"title": "Fight Club", "type": "Movie", "description": "Psychological thriller about an insomniac office worker who forms an underground fight club. Dark commentary on consumerism and masculinity.", "genres": ["Drama", "Thriller"], "year": 1999, "rating": 8.8}, {"title": "The Lord of the Rings: The Fellowship of the Ring", "type": "Movie", "description": "Fantasy epic where a hobbit inherits a ring of power and must destroy it. Beginning of the epic journey across Middle-earth with a fellowship of heroes.", "genres": ["Adventure", "Drama", "Fantasy"], "year": 2001, "rating": 8.8}, {"title": "Star Wars: Episode IV - A New Hope", "type": "Movie", "description": "Space opera about a young farmer who joins a rebellion against an evil empire. Classic hero's journey with iconic characters and groundbreaking special effects.", "genres": ["Adventure", "Fantasy", "Sci-Fi"], "year": 1977, "rating": 8.6}, {"title": "The Lord of the Rings: The Two Towers", "type": "Movie", "description": "Fantasy epic continuing the quest to destroy the One Ring. The fellowship splits as they face new challenges and the corruption of power.", "genres": ["Adventure", "Drama", "Fantasy"], "year": 2002, "rating": 8.7}, {"title": "Goodfellas", "type": "Movie", "description": "Crime biographical film about the rise and fall of a mob associate. Based on a true story, showcasing the glamorous and violent world of organized crime.", "genres": ["Biography", "Crime", "Drama"], "year": 1990, "rating": 8.7}, {"title": "The Silence of the Lambs", "type": "Movie", "description": "Psychological horror thriller about an FBI trainee seeking help from imprisoned cannibal <PERSON> to catch another killer. Intense cat-and-mouse game.", "genres": ["Crime", "Drama", "Thriller"], "year": 1991, "rating": 8.6}, {"title": "Saving Private <PERSON>", "type": "Movie", "description": "War drama about soldiers searching for a paratrooper behind enemy lines during D-Day. Realistic portrayal of World War II combat and sacrifice.", "genres": ["Drama", "War"], "year": 1998, "rating": 8.6}, {"title": "The Green Mile", "type": "Movie", "description": "Supernatural drama about a death row corrections officer who discovers one of his charges has a miraculous gift. Story of compassion and redemption.", "genres": ["Crime", "Drama", "Fantasy"], "year": 1999, "rating": 8.6}, {"title": "Se7en", "type": "Movie", "description": "Neo-noir psychological thriller about two detectives hunting a serial killer who uses the seven deadly sins as motives. Dark and disturbing crime story.", "genres": ["Crime", "Drama", "Mystery"], "year": 1995, "rating": 8.6}, {"title": "Titanic", "type": "Movie", "description": "Romance and disaster epic about the ill-fated maiden voyage of RMS Titanic. Love story between passengers from different social classes.", "genres": ["Drama", "Romance"], "year": 1997, "rating": 7.9}, {"title": "Avatar", "type": "Movie", "description": "Sci-fi epic about a paraplegic marine who infiltrates an alien world through an avatar body. Environmental themes with groundbreaking 3D technology.", "genres": ["Action", "Adventure", "Fantasy"], "year": 2009, "rating": 7.9}, {"title": "Avengers: Endgame", "type": "Movie", "description": "Superhero epic conclusion to the Marvel Cinematic Universe's Infinity Saga. The Avengers attempt to reverse <PERSON><PERSON>' devastating snap.", "genres": ["Action", "Adventure", "Drama"], "year": 2019, "rating": 8.4}, {"title": "The Departed", "type": "Movie", "description": "Crime thriller about an undercover cop and a police informant in the Boston mob. Psychological tension builds as both sides try to expose the other.", "genres": ["Crime", "Drama", "Thriller"], "year": 2006, "rating": 8.5}, {"title": "The Prestige", "type": "Movie", "description": "Mystery thriller about two rival magicians in Victorian London. Their obsession with creating the perfect illusion leads to dangerous consequences.", "genres": ["Drama", "Mystery", "Sci-Fi"], "year": 2006, "rating": 8.5}, {"title": "Gladiator", "type": "Movie", "description": "Historical epic about a Roman general who becomes a gladiator seeking revenge against the emperor who murdered his family. Action and political intrigue.", "genres": ["Action", "Adventure", "Drama"], "year": 2000, "rating": 8.5}, {"title": "The Sopranos", "type": "TV Show", "description": "Crime drama about a New Jersey mob boss balancing family life with running a criminal organization. Groundbreaking psychological depth in television.", "genres": ["Crime", "Drama"], "year": 1999, "rating": 9.2}, {"title": "The Wire", "type": "TV Show", "description": "Crime drama exploring Baltimore's drug scene from multiple perspectives - police, dealers, politicians, and media. Realistic portrayal of urban decay.", "genres": ["Crime", "Drama", "Thriller"], "year": 2002, "rating": 9.3}, {"title": "True Detective", "type": "TV Show", "description": "Anthology crime series with each season featuring different characters and mysteries. Philosophical themes and atmospheric storytelling.", "genres": ["Crime", "Drama", "Mystery"], "year": 2014, "rating": 8.9}, {"title": "<PERSON>", "type": "TV Show", "description": "Modern adaptation of <PERSON>'s detective stories set in 21st century London. Brilliant deduction and contemporary technology.", "genres": ["Crime", "Drama", "Mystery"], "year": 2010, "rating": 9.1}, {"title": "Westworld", "type": "TV Show", "description": "Sci-fi series about a futuristic theme park populated by android hosts. Explores consciousness, free will, and the nature of humanity.", "genres": ["Drama", "Mystery", "Sci-Fi"], "year": 2016, "rating": 8.6}, {"title": "The Crown", "type": "TV Show", "description": "Historical drama chronicling the reign of Queen <PERSON> and the British royal family. Political and personal challenges across decades.", "genres": ["Biography", "Drama", "History"], "year": 2016, "rating": 8.7}, {"title": "<PERSON><PERSON><PERSON>", "type": "TV Show", "description": "Crime drama about the Colombian drug cartels and DEA agents trying to bring them down. Based on true events of the cocaine trade.", "genres": ["Biography", "Crime", "Drama"], "year": 2015, "rating": 8.8}, {"title": "House of Cards", "type": "TV Show", "description": "Political drama about a ruthless politician's rise to power in Washington D.C. Manipulation, corruption, and ambition in American politics.", "genres": ["Drama"], "year": 2013, "rating": 8.7}, {"title": "Mindhunter", "type": "TV Show", "description": "Crime series about FBI agents in the late 1970s interviewing imprisoned serial killers to solve ongoing cases. Psychological profiling origins.", "genres": ["Crime", "Drama", "Thriller"], "year": 2017, "rating": 8.6}, {"title": "Black Mirror", "type": "TV Show", "description": "Anthology sci-fi series exploring dark aspects of technology and society. Each episode presents a dystopian vision of near-future scenarios.", "genres": ["Drama", "Sci-Fi", "Thriller"], "year": 2011, "rating": 8.8}, {"title": "Better Call Saul", "type": "TV Show", "description": "Crime drama prequel to Breaking Bad focusing on lawyer <PERSON>'s transformation into <PERSON>. Character study of moral decline.", "genres": ["Crime", "Drama"], "year": 2015, "rating": 8.8}, {"title": "Fargo", "type": "TV Show", "description": "Anthology crime series inspired by the <PERSON><PERSON> Brothers film. Dark comedy and crime in small-town America with quirky characters.", "genres": ["Crime", "Drama", "Thriller"], "year": 2014, "rating": 8.9}, {"title": "The Mandalorian", "type": "TV Show", "description": "Star Wars series about a bounty hunter protecting a mysterious child in the outer rim. Space western with practical effects and puppetry.", "genres": ["Action", "Adventure", "Fantasy"], "year": 2019, "rating": 8.7}, {"title": "Chernobyl", "type": "TV Show", "description": "Historical drama miniseries about the 1986 nuclear disaster and its aftermath. Harrowing portrayal of the worst nuclear accident in history.", "genres": ["Drama", "History", "Thriller"], "year": 2019, "rating": 9.4}, {"title": "The Witcher", "type": "TV Show", "description": "Fantasy series about <PERSON><PERSON><PERSON> of Rivia, a monster hunter in a world where people often prove more wicked than beasts. Based on Polish novels.", "genres": ["Action", "Adventure", "Drama"], "year": 2019, "rating": 8.2}, {"title": "Money Heist", "type": "TV Show", "description": "Spanish heist thriller about a criminal mastermind and his team robbing the Royal Mint of Spain. Complex plot with flashbacks and character development.", "genres": ["Action", "Crime", "Drama"], "year": 2017, "rating": 8.3}, {"title": "Dark", "type": "TV Show", "description": "German sci-fi thriller about time travel and family secrets across multiple generations. Complex narrative spanning different time periods.", "genres": ["Crime", "Drama", "Mystery"], "year": 2017, "rating": 8.8}, {"title": "The Boys", "type": "TV Show", "description": "Superhero satire about a group of vigilantes fighting corrupt superheroes. Dark and violent deconstruction of the superhero genre.", "genres": ["Action", "Comedy", "Crime"], "year": 2019, "rating": 8.7}, {"title": "Ozark", "type": "TV Show", "description": "Crime drama about a financial advisor who must launder money for a Mexican cartel while relocating his family to the Ozarks.", "genres": ["Crime", "Drama", "Thriller"], "year": 2017, "rating": 8.4}, {"title": "The Queen's Gambit", "type": "TV Show", "description": "Coming-of-age drama about a chess prodigy's rise to the top of the chess world while struggling with addiction. 1960s period piece.", "genres": ["Drama"], "year": 2020, "rating": 8.5}, {"title": "Squid Game", "type": "TV Show", "description": "Korean survival drama about desperate people competing in children's games for a massive cash prize. Social commentary on economic inequality.", "genres": ["Action", "Drama", "Mystery"], "year": 2021, "rating": 8.0}]