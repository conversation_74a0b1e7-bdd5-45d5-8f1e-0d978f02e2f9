# Sample Questions for Entertainment Bot (English)

## 🎭 By Genre & Mood

### Action & Adventure

- "Action movies like The Matrix"
- "High-octane thrillers with car chases"
- "Superhero movies with great visual effects"
- "Adventure films set in exotic locations"
- "Fast-paced action comedies"

### Science Fiction

- "Sci-fi movies like Inception"
- "Space exploration films"
- "Time travel stories"
- "Dystopian future movies"
- "Mind-bending sci-fi thrillers"
- "Movies about artificial intelligence"

### Drama & Psychological

- "Dark psychological thrillers"
- "Character-driven dramas"
- "Movies about family relationships"
- "Emotional coming-of-age stories"
- "Films exploring human nature"

### Comedy

- "Feel-good comedy TV shows"
- "Romantic comedies from the 90s"
- "Workplace comedy series"
- "Stand-up comedy specials"
- "Comedy shows about friendship"

### Fantasy & Adventure

- "Epic fantasy series like Game of Thrones"
- "Medieval fantasy movies"
- "Magic and wizardry shows"
- "Mythological adventure films"

### Horror & Thriller

- "Psychological horror movies"
- "Supernatural thriller series"
- "Classic horror films"
- "Mystery thriller shows"

### Crime & Mystery

- "Crime drama series"
- "Detective mystery shows"
- "Heist movies"
- "Police procedural dramas"
- "True crime documentaries"

## ⭐ By Quality & Ratings

### High-Rated Content

- "High-rated crime dramas"
- "Best movies from the 2000s"
- "Top-rated TV series of all time"
- "Award-winning documentaries"
- "Critically acclaimed foreign films"
- "Movies with 9+ IMDb rating"

### Era-Specific

- "Classic movies from the 1990s"
- "Best films of the 21st century"
- "Iconic movies from the 80s"
- "Modern masterpieces"

## 👥 By Cast & Director

### Specific Actors

- "Movies with Leonardo DiCaprio"
- "Tom Hanks drama films"
- "Shows with strong female leads"
- "Movies starring Meryl Streep"
- "Action films with Keanu Reeves"

### Directors

- "Christopher Nolan films"
- "Quentin Tarantino movies"
- "Steven Spielberg classics"
- "Martin Scorsese crime films"
- "Denis Villeneuve sci-fi movies"

### Ensemble Cast

- "Shows with great ensemble cast"
- "Movies with multiple storylines"
- "Films with large cast of characters"

## 🎯 By Themes & Elements

### Relationships & Emotions

- "Stories about friendship and loyalty"
- "Movies about love and sacrifice"
- "Shows exploring family dynamics"
- "Films about forgiveness"
- "Stories of redemption"

### Philosophical & Deep

- "Movies exploring reality and illusion"
- "Films about the meaning of life"
- "Shows questioning human existence"
- "Movies with plot twists"
- "Stories about moral dilemmas"

### Specific Elements

- "Time travel movies"
- "Movies with unreliable narrators"
- "Shows with parallel universes"
- "Films about memory and identity"
- "Stories set in virtual reality"

## 🌍 By Setting & Time Period

### Historical

- "World War II movies"
- "Medieval period dramas"
- "Victorian era films"
- "Ancient civilization stories"

### Modern & Future

- "Contemporary urban dramas"
- "Movies set in the near future"
- "Post-apocalyptic shows"
- "Cyberpunk films"

### Locations

- "Movies set in New York City"
- "Films taking place in space"
- "Shows set in small towns"
- "Movies in exotic locations"

## 🎬 By Comparison & Similarity

### Similar to Popular Titles

- "Movies like Inception"
- "Shows similar to Breaking Bad"
- "Films like The Godfather"
- "Series like Stranger Things"
- "Movies in the style of Pulp Fiction"

### Franchise & Series

- "Marvel universe movies"
- "Movies like The Lord of the Rings"
- "Shows similar to The Office"
- "Films like Star Wars"

## 🎵 By Mood & Atmosphere

### Feel-Good

- "Uplifting movies for a bad day"
- "Feel-good comedies"
- "Heartwarming family films"
- "Inspirational true stories"

### Dark & Intense

- "Dark and gritty crime shows"
- "Intense psychological dramas"
- "Disturbing thriller movies"
- "Complex moral stories"

### Light & Fun

- "Light entertainment for weekends"
- "Fun adventure movies"
- "Easy-watching comedy series"
- "Relaxing shows to unwind"

## 🔍 Complex Multi-Factor Queries

### Combining Multiple Elements

- "High-rated sci-fi movies with Leonardo DiCaprio"
- "Dark psychological thrillers from the 2000s"
- "Comedy series about friendship with great ratings"
- "Award-winning dramas about family relationships"
- "Complex sci-fi movies with plot twists"
- "Crime drama series with excellent character development"
- "Movies like Inception but more recent"
- "Shows similar to Game of Thrones but shorter"

### Specific Mood + Genre

- "Mind-bending thrillers that make you think"
- "Emotional dramas that will make me cry"
- "Action movies that are also funny"
- "Horror films that are more psychological than gory"
- "Sci-fi shows with strong character relationships"

## ❌ Queries That Might Not Have Results

### Outside Database Scope

- "Bollywood romantic musicals"
- "Korean historical dramas"
- "Japanese anime series"
- "Documentary films about nature"
- "Stand-up comedy specials"
- "Silent films from the 1920s"
- "Independent foreign art films"

## 💡 Testing Tips

### For Developers

- Test with exact titles: "Movies like The Matrix"
- Test with genre combinations: "Sci-fi action thriller"
- Test with actor/director names: "Christopher Nolan films"
- Test with mood descriptions: "Something dark and complex"
- Test with time periods: "Movies from the 1990s"
- Test with themes: "Stories about redemption"
- Test edge cases: "Animated movies" (likely no results)
- Test vague queries: "Something good to watch"
- Test specific requirements: "Movies under 2 hours"

### Response Quality Check

- Does the bot acknowledge the user's request?
- Are recommendations relevant to the query?
- Does it explain why recommendations match?
- Are movie/TV show titles mentioned clearly?
- Is the response conversational and engaging?
