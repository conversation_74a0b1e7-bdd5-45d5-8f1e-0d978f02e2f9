# Azure OpenAI Configuration
# Copy this file to .env and fill in your actual values

# Azure OpenAI API Version
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Azure OpenAI Endpoint URL (replace 'your-resource-name' with actual resource name)
AZURE_OPENAI_API_BASE=https://your-resource-name.openai.azure.com/

# Azure OpenAI API Key (get from Azure Portal)
AZURE_OPENAI_API_KEY=your-api-key-here

# Deployment name of your model (e.g., gpt-4, gpt-35-turbo)
AZURE_OPENAI_DEPLOYMENT_NAME=GPT-4o-mini
